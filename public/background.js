chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'LOG_ISSUE') {
    console.error('Vue App Issue:', message.error);
  }

  if (message.action === 'savePDF') {
   
     chrome.tabs.create({ url: 'https://www.youtube.com/watch?v=oQDO0zooANA'}, () => {
        sendResponse({ success: true });
      });
  
  }

});


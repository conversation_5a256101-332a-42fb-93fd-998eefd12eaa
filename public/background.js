chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'LOG_ISSUE') {
    console.error('Vue App Issue:', message.error);
  }

  if (message.action === 'savePDF') {
    try {
      // Convert blob data to array buffer
      const pdfBlob = message.data;

      // Create a download using Chrome's downloads API
      const reader = new FileReader();
      reader.onload = function() {
        const arrayBuffer = reader.result;
        const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `labels_${timestamp}.pdf`;

        chrome.downloads.download({
          url: url,
          filename: filename,
          saveAs: true
        }, (downloadId) => {
          if (chrome.runtime.lastError) {
            console.error('Download failed:', chrome.runtime.lastError);
            sendResponse({ success: false, error: chrome.runtime.lastError.message });
          } else {
            console.log('PDF download started with ID:', downloadId);
            sendResponse({ success: true, downloadId: downloadId });
            // Clean up the object URL after a delay
            setTimeout(() => URL.revokeObjectURL(url), 1000);
          }
        });
      };

      reader.onerror = function() {
        console.error('Failed to read PDF blob');
        sendResponse({ success: false, error: 'Failed to read PDF data' });
      };

      reader.readAsArrayBuffer(pdfBlob);

    } catch (error) {
      console.error('PDF save error:', error);
      sendResponse({ success: false, error: error.message });
    }

    // Return true to indicate we'll send response asynchronously
    return true;
  }

});


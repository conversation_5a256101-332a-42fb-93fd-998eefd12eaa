// Test utility for auto-mapping functionality
import { AutoMappingService, TEMPLATE_HEADERS, HEADER_ALIASES } from '@/services/autoMappingService'
import { requiredFields } from '@/stores/label'

/**
 * Test auto-mapping with different CSV header scenarios
 */
export function testAutoMapping(): void {
  console.log('🧪 Testing Auto-Mapping Functionality')
  console.log('=====================================')

  // Test Case 1: Perfect template match
  console.log('\n📋 Test Case 1: Perfect Template Match')
  const templateHeaders = [...TEMPLATE_HEADERS]
  const templateMappings = JSON.parse(JSON.stringify(requiredFields))
  const templateResult = AutoMappingService.autoMapFields(templateHeaders, templateMappings)
  
  console.log('Headers:', templateHeaders)
  console.log('Result:', templateResult)
  console.log('✅ Expected: All fields mapped, template detected')

  // Test Case 2: Alternative header names
  console.log('\n📋 Test Case 2: Alternative Header Names')
  const altHeaders = [
    'Customer Name',
    'Customer Address', 
    'Phone Number',
    'Company Name',
    'Company Address',
    'Business Phone',
    'Product List'
  ]
  const altMappings = JSON.parse(JSON.stringify(requiredFields))
  const altResult = AutoMappingService.autoMapFields(altHeaders, altMappings)
  
  console.log('Headers:', altHeaders)
  console.log('Result:', altResult)
  console.log('✅ Expected: Most fields mapped via aliases')

  // Test Case 3: Mixed case and spacing
  console.log('\n📋 Test Case 3: Mixed Case and Spacing')
  const mixedHeaders = [
    'RECIPIENT_NAME',
    'recipient address',
    'Recipient Phone',
    'vendor_name',
    'VENDOR ADDRESS',
    'vendor phone',
    'Items'
  ]
  const mixedMappings = JSON.parse(JSON.stringify(requiredFields))
  const mixedResult = AutoMappingService.autoMapFields(mixedHeaders, mixedMappings)
  
  console.log('Headers:', mixedHeaders)
  console.log('Result:', mixedResult)
  console.log('✅ Expected: All fields mapped despite case differences')

  // Test Case 4: Partial match scenario
  console.log('\n📋 Test Case 4: Partial Match Scenario')
  const partialHeaders = [
    'Customer Name',
    'Delivery Address',
    'Contact Number',
    'Unknown Column 1',
    'Random Data',
    'Another Field'
  ]
  const partialMappings = JSON.parse(JSON.stringify(requiredFields))
  const partialResult = AutoMappingService.autoMapFields(partialHeaders, partialMappings)
  
  console.log('Headers:', partialHeaders)
  console.log('Result:', partialResult)
  console.log('✅ Expected: Some fields mapped, some unmapped')

  // Test Case 5: No matches
  console.log('\n📋 Test Case 5: No Matches')
  const noMatchHeaders = [
    'Column A',
    'Column B',
    'Column C',
    'Data 1',
    'Data 2',
    'Random Field'
  ]
  const noMatchMappings = JSON.parse(JSON.stringify(requiredFields))
  const noMatchResult = AutoMappingService.autoMapFields(noMatchHeaders, noMatchMappings)
  
  console.log('Headers:', noMatchHeaders)
  console.log('Result:', noMatchResult)
  console.log('✅ Expected: No fields mapped')

  console.log('\n🎉 Auto-mapping tests completed!')
}

/**
 * Test template detection
 */
export function testTemplateDetection(): void {
  console.log('\n🔍 Testing Template Detection')
  console.log('==============================')

  const testCases = [
    {
      name: 'Perfect Template',
      headers: [...TEMPLATE_HEADERS],
      expected: true
    },
    {
      name: 'Template with Extra Columns',
      headers: [...TEMPLATE_HEADERS, 'extra_column', 'another_field'],
      expected: true
    },
    {
      name: 'Template Missing One Field',
      headers: TEMPLATE_HEADERS.slice(0, -1),
      expected: false
    },
    {
      name: 'Mixed Case Template',
      headers: TEMPLATE_HEADERS.map(h => h.toUpperCase()),
      expected: true
    },
    {
      name: 'Completely Different Headers',
      headers: ['col1', 'col2', 'col3', 'col4'],
      expected: false
    }
  ]

  testCases.forEach(testCase => {
    const result = AutoMappingService.detectTemplate(testCase.headers)
    const status = result === testCase.expected ? '✅' : '❌'
    console.log(`${status} ${testCase.name}: ${result} (expected: ${testCase.expected})`)
    console.log(`   Headers: ${testCase.headers.join(', ')}`)
  })
}

/**
 * Test header aliases
 */
export function testHeaderAliases(): void {
  console.log('\n🔗 Testing Header Aliases')
  console.log('=========================')

  Object.entries(HEADER_ALIASES).forEach(([fieldKey, aliases]) => {
    console.log(`\n📝 ${fieldKey}:`)
    aliases.forEach(alias => {
      console.log(`   - "${alias}"`)
    })
  })
}

/**
 * Generate test CSV content for different scenarios
 */
export function generateTestCSV(scenario: 'template' | 'aliases' | 'mixed' | 'partial'): string {
  const scenarios = {
    template: {
      headers: [...TEMPLATE_HEADERS],
      data: [
        ['John Doe', '123 Main St, City, ST 12345', '******-123-4567', 'ABC Corp', '456 Business Ave, Town, ST 67890', '******-987-6543', 'Widget A, Widget B'],
        ['Jane Smith', '789 Oak Rd, Village, ST 54321', '******-234-5678', 'XYZ Inc', '321 Industry Blvd, Metro, ST 09876', '******-876-5432', 'Product X, Product Y']
      ]
    },
    aliases: {
      headers: ['Customer Name', 'Customer Address', 'Phone Number', 'Company Name', 'Company Address', 'Business Phone', 'Product List'],
      data: [
        ['Alice Johnson', '456 Pine St, Hometown, ST 98765', '******-345-6789', 'DEF Enterprises', '654 Corporate Dr, Business Park, ST 56789', '******-765-4321', 'Service A, Service B'],
        ['Bob Wilson', '321 Elm Ave, Suburb, ST 13579', '******-456-7890', 'GHI Solutions', '987 Innovation Way, Tech City, ST 24680', '******-654-3210', 'Item X, Item Y, Item Z']
      ]
    },
    mixed: {
      headers: ['RECIPIENT_NAME', 'recipient address', 'Recipient Phone', 'vendor_name', 'VENDOR ADDRESS', 'vendor phone', 'Items'],
      data: [
        ['Charlie Brown', '159 Maple Dr, Anytown, ST 75319', '******-567-8901', 'JKL Corp', '753 Commerce St, Trade City, ST 86420', '******-543-2109', 'Tool A, Tool B'],
        ['Diana Prince', '357 Cedar Ln, Somewhere, ST 95173', '******-678-9012', 'MNO Ltd', '951 Enterprise Rd, Business Hub, ST 73951', '******-432-1098', 'Device X, Device Y']
      ]
    },
    partial: {
      headers: ['Customer Name', 'Delivery Address', 'Contact Number', 'Unknown Column', 'Random Data', 'Extra Field'],
      data: [
        ['Eve Adams', '246 Birch St, Newtown, ST 64208', '******-789-0123', 'Unknown Value', 'Random Text', 'Extra Data'],
        ['Frank Miller', '468 Spruce Ave, Oldtown, ST 31975', '******-890-1234', 'Another Value', 'More Random', 'Additional Info']
      ]
    }
  }

  const selected = scenarios[scenario]
  const csvLines = [
    selected.headers.join(','),
    ...selected.data.map(row => row.map(cell => `"${cell}"`).join(','))
  ]

  return csvLines.join('\n')
}

// Export for use in browser console during development
if (import.meta.env.DEV) {
  // Make functions available globally for testing
  (window as any).testAutoMapping = testAutoMapping;
  (window as any).testTemplateDetection = testTemplateDetection;
  (window as any).testHeaderAliases = testHeaderAliases;
  (window as any).generateTestCSV = generateTestCSV;
  
  console.log('🔧 Auto-mapping test functions available:')
  console.log('  - testAutoMapping()')
  console.log('  - testTemplateDetection()')
  console.log('  - testHeaderAliases()')
  console.log('  - generateTestCSV("template"|"aliases"|"mixed"|"partial")')
}

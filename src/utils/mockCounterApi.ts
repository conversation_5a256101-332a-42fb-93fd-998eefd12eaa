// Mock API for development/testing purposes
// This simulates a real server endpoint that returns label counts

interface MockCounterResponse {
  count: number
  timestamp: string
  success: boolean
}

class MockCounterAPI {
  private baseCount: number = 100000
  private lastUpdate: number = Date.now()
  private incrementRate: number = 1 // labels per second on average

  // Simulate realistic counter growth
  private calculateCurrentCount(): number {
    const now = Date.now()
    const secondsElapsed = (now - this.lastUpdate) / 1000

    // Add some randomness to make it feel more realistic
    const randomFactor = 0.5 + Math.random() // 0.5 to 1.5 multiplier
    const increment = Math.floor(secondsElapsed * this.incrementRate * randomFactor)

    this.baseCount += increment
    this.lastUpdate = now

    return this.baseCount
  }

  // Mock API endpoint
  async getCount(): Promise<MockCounterResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200))

    // Simulate occasional failures (5% chance)
    if (Math.random() < 0.05) {
      throw new Error('Network error: Unable to fetch counter')
    }

    const count = this.calculateCurrentCount()

    return {
      count,
      timestamp: new Date().toISOString(),
      success: true
    }
  }

  // Reset counter for testing
  reset(newBaseCount: number = 100000): void {
    this.baseCount = newBaseCount
    this.lastUpdate = Date.now()
  }

  // Set increment rate (labels per second)
  setIncrementRate(rate: number): void {
    this.incrementRate = Math.max(0, rate)
  }

  // Add method to handle counter updates
  async updateCount(increment: number): Promise<{ success: boolean; newTotal: number; timestamp: string }> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100))

    // Simulate occasional failures (2% chance)
    if (Math.random() < 0.02) {
      throw new Error('Network error: Unable to update counter')
    }

    this.baseCount += increment
    this.lastUpdate = Date.now()

    return {
      success: true,
      newTotal: this.baseCount,
      timestamp: new Date().toISOString()
    }
  }
}

// Create singleton instance
export const mockCounterAPI = new MockCounterAPI()

// Helper function to setup mock fetch for development
export function setupMockCounterAPI(baseEndpoint: string = 'https://api.lavvel.com/counter'): void {
  // Only setup in development mode
  if (import.meta.env.DEV) {
    console.log('Setting up mock counter API for development')

    // Store original fetch
    const originalFetch = window.fetch

    // Override fetch for our specific endpoints
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const url = typeof input === 'string' ? input : input.toString()
      const method = init?.method?.toUpperCase() || 'GET'

      // Handle GET requests to the counter endpoint
      if (url === baseEndpoint && method === 'GET') {
        try {
          const data = await mockCounterAPI.getCount()
          return new Response(JSON.stringify(data), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          })
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          })
        }
      }

      // Handle POST requests to the update endpoint
      if ((url === `${baseEndpoint}/update` || url === baseEndpoint) && method === 'POST') {
        try {
          const requestBody = init?.body ? JSON.parse(init.body as string) : {}
          const increment = requestBody.count || requestBody.labelsGenerated || 1

          const data = await mockCounterAPI.updateCount(increment)
          return new Response(JSON.stringify(data), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          })
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          })
        }
      }

      // Use original fetch for other requests
      return originalFetch(input, init)
    }
  }
}

// Utility functions for testing
export const counterTestUtils = {
  // Simulate a big jump in counter (e.g., bulk import)
  simulateBulkIncrease: (amount: number) => {
    mockCounterAPI.reset(mockCounterAPI['baseCount'] + amount)
  },

  // Reset to specific value
  resetTo: (value: number) => {
    mockCounterAPI.reset(value)
  },

  // Set growth rate
  setGrowthRate: (labelsPerSecond: number) => {
    mockCounterAPI.setIncrementRate(labelsPerSecond)
  }
}

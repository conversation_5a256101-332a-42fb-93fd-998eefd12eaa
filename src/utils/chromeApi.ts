// src/utils/chromeApi.ts

// Type definition for Chrome's runtime API
interface ChromeRuntime {
  sendMessage: (
    message: any,
    callback: (response: any) => void
  ) => void;
}

// Type definition for the global Chrome object
interface Chrome {
  runtime: ChromeRuntime;
}

// Declare global Chrome object
declare global {
  interface Window {
    chrome?: Chrome;
  }
}

// Wrapper function for Chrome's sendMessage
export function sendChromeMessage(
  message: any,
  callback: (response: any) => void
): void {
  if (window.chrome && window.chrome.runtime) {
    window.chrome.runtime.sendMessage(message, callback);
  } else {
    console.warn('Chrome API is not available. Are you in development mode?');
    // Simulated response for development environment
    setTimeout(() => {
      callback({ success: true, devMode: true });
    }, 100);
  }
}


// src/utils/chromeApi.ts

// Type definition for Chrome's runtime API
interface ChromeRuntime {
  sendMessage: (
    message: any,
    callback: (response: any) => void
  ) => void;
}

// Type definition for the global Chrome object
interface Chrome {
  runtime: ChromeRuntime;
}

// Declare global Chrome object
declare global {
  interface Window {
    chrome?: Chrome;
  }
}

// Wrapper function for Chrome's sendMessage
export function sendChromeMessage(
  message: any,
  callback: (response: any) => void
): void {
  if (window.chrome && window.chrome.runtime) {
    console.log('Sending message to Chrome extension:', message.action);
    window.chrome.runtime.sendMessage(message, (response) => {
      // Check for Chrome runtime errors
      const lastError = (window.chrome?.runtime as any)?.lastError;
      if (lastError) {
        console.error('Chrome runtime error:', lastError);
        callback({ success: false, error: lastError.message || 'Chrome runtime error' });
      } else {
        console.log('Chrome extension response:', response);
        callback(response);
      }
    });
  } else {
    console.warn('Chrome API is not available. Are you in development mode?');
    // In development mode, we'll indicate failure so the fallback download is used
    setTimeout(() => {
      callback({ success: false, error: 'Chrome API not available (development mode)', devMode: true });
    }, 100);
  }
}


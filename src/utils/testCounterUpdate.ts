// Test utility for counter update functionality
import { updateServerCounter, updateServerCounterSilent } from '@/services/counterUpdateService'

/**
 * Test the counter update functionality
 */
export async function testCounterUpdate(): Promise<void> {
  console.log('🧪 Testing counter update functionality...')
  
  try {
    // Test with a small number of labels
    console.log('📊 Testing counter update with 5 labels...')
    const result = await updateServerCounter(5, 1024 * 50) // 50KB PDF
    console.log('✅ Counter update successful:', result)
    
    // Test silent update
    console.log('🔇 Testing silent counter update with 3 labels...')
    await updateServerCounterSilent(3, 1024 * 30) // 30KB PDF
    console.log('✅ Silent counter update completed')
    
    console.log('🎉 All counter update tests passed!')
    
  } catch (error) {
    console.error('❌ Counter update test failed:', error)
  }
}

/**
 * Simulate a PDF generation and counter update
 */
export async function simulatePDFGeneration(labelCount: number = 10): Promise<void> {
  console.log(`🎯 Simulating PDF generation for ${labelCount} labels...`)
  
  try {
    // Simulate PDF generation time
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Simulate PDF size (roughly 50KB per label)
    const estimatedPDFSize = labelCount * 50 * 1024
    
    // Update counter
    const result = await updateServerCounter(labelCount, estimatedPDFSize)
    
    console.log(`✅ Simulated PDF generation complete!`)
    console.log(`📈 Counter updated: +${labelCount} labels`)
    console.log(`📊 New total: ${result.newTotal}`)
    console.log(`📁 PDF size: ${(estimatedPDFSize / 1024).toFixed(1)}KB`)
    
  } catch (error) {
    console.error('❌ Simulation failed:', error)
  }
}

/**
 * Test different scenarios
 */
export async function runCounterUpdateTests(): Promise<void> {
  console.log('🚀 Running comprehensive counter update tests...')
  
  const testCases = [
    { labels: 1, description: 'Single label' },
    { labels: 5, description: 'Small batch' },
    { labels: 25, description: 'Medium batch' },
    { labels: 100, description: 'Large batch' }
  ]
  
  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.description} (${testCase.labels} labels)`)
    await simulatePDFGeneration(testCase.labels)
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  
  console.log('\n🎊 All tests completed!')
}

// Export for use in browser console during development
if (import.meta.env.DEV) {
  // Make functions available globally for testing
  (window as any).testCounterUpdate = testCounterUpdate;
  (window as any).simulatePDFGeneration = simulatePDFGeneration;
  (window as any).runCounterUpdateTests = runCounterUpdateTests;
  
  console.log('🔧 Counter update test functions available:')
  console.log('  - testCounterUpdate()')
  console.log('  - simulatePDFGeneration(labelCount)')
  console.log('  - runCounterUpdateTests()')
}

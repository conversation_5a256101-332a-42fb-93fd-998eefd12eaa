export interface FormattedLabel {
  recipient: {
    name: string;
    address: string;
    phoneNumber: string;
  };
  vendor: {
    name: string;
    address: string;
    phoneNumber: string;
  };
  items: string[];
}

export const formatLabelData = (label: Record<string, string>): FormattedLabel => {
  return {
    recipient: {
      name: label.recipient_name || '',
      address: label.recipient_address || '',
      phoneNumber: label.recipient_phone || ''
    },
    vendor: {
      name: label.vendor_name || '',
      address: label.vendor_address || '',
      phoneNumber: label.vendor_phone || ''
    },
    items: label.items?.split(',').map(item => item.trim()) || []
  }
}

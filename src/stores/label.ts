import { ref, computed, h, createApp } from 'vue'
import { defineStore } from 'pinia'
import Papa from 'papaparse'
import { type Dimension } from '@/lib/utils'
import { sendChromeMessage } from '@/utils/chromeApi'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import { LabelPage } from '@/components/label'
import { formatLabelData, type FormattedLabel } from '@/utils/labelUtils'

export interface FieldMapping {
  key: string
  label: string
  required: boolean
  mappedTo: string | null
}

export const requiredFields: FieldMapping[] = [
  { key: 'recipient_name', label: 'Recipient Name', required: true, mappedTo: null },
  { key: 'recipient_address', label: 'Recipient Address', required: true, mappedTo: null },
  { key: 'recipient_phone', label: 'Recipient Phone', required: true, mappedTo: null },
  { key: 'vendor_name', label: 'Vendor Name', required: true, mappedTo: null },
  { key: 'vendor_address', label: 'Vendor Address', required: true, mappedTo: null },
  { key: 'vendor_phone', label: 'Vendor Phone', required: true, mappedTo: null },
  { key: 'items', label: 'Items', required: true, mappedTo: null },
]

export const useLabelStore = defineStore('label', () => {
  const rawData = ref<Record<string, string>[]>([])
  const importedLabels = ref<Record<string, string>[]>([])
  const originalHeaders = ref<string[]>([])
  const fieldMappings = ref<FieldMapping[]>(JSON.parse(JSON.stringify(requiredFields)))
  const isExporting = ref(false)
  const exportError = ref<string | null>(null)
  const isValidating = ref(false)
  const validationConfirmed = ref(false)
  const isSuccessfullyExported = ref(false)

  const processAndUploadLabels = async (files: File[] | null) => {
    if (!files) return

    const file = files[0]
    const reader = new FileReader()

    reader.onload = async (event) => {
      const csvData = event.target?.result as string
      const { data, headers } = await csvToJson(csvData)
      rawData.value = data
      originalHeaders.value = headers
      // Set imported labels to raw data initially
      importedLabels.value = data
      // Reset mappings
      fieldMappings.value = JSON.parse(JSON.stringify(requiredFields))
      // Set validation state
      isValidating.value = true
      validationConfirmed.value = false
    }

    reader.readAsText(file)
  }

  const csvToJson = async (csv: string): Promise<{ data: Record<string, string>[], headers: string[] }> => {
    const result = Papa.parse(csv, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header: string) => header.trim(),
    })

    if (result.errors.length > 0) {
      console.error('CSV parsing errors:', result.errors)
    }

    // Extract headers from the first row
    const headers = Object.keys(result.data[0] || {})

    return {
      data: result.data as Record<string, string>[],
      headers
    }
  }

  const updateFieldMapping = (fieldKey: string, headerName: string | null) => {
    const mapping = fieldMappings.value.find(m => m.key === fieldKey)
    if (mapping) {
      mapping.mappedTo = headerName
      console.log(`Updated mapping for ${fieldKey} to ${headerName}`)
    }
  }

  const applyFieldMappings = () => {
    importedLabels.value = rawData.value.map(row => {
      const mappedRow: Record<string, string> = {}
      fieldMappings.value.forEach(mapping => {
        if (mapping.mappedTo) {
          mappedRow[mapping.key] = row[mapping.mappedTo] || ''
        }
      })
      return mappedRow
    })
  }

  const confirmValidation = () => {
    applyFieldMappings()
    isValidating.value = false
    validationConfirmed.value = true
  }

  const cancelValidation = () => {
    isValidating.value = false
    rawData.value = []
    importedLabels.value = []
    originalHeaders.value = []
    fieldMappings.value = JSON.parse(JSON.stringify(requiredFields))
    validationConfirmed.value = false
  }

  const hasImportedLabels = computed(() => rawData.value.length > 0)

  const isMappingComplete = computed(() => {
    return fieldMappings.value.every(mapping =>
      !mapping.required || (mapping.mappedTo !== null && mapping.mappedTo !== '')
    )
  }
  )

  const generateLabels = async (dimensions: Dimension, labels: Record<string, string>[]) => {
    isExporting.value = true
    exportError.value = null
    isSuccessfullyExported.value = false

    try {
      // Create container div for all labels
      const container = document.createElement('div')
      container.style.position = 'absolute'
      container.style.left = '-9999px'
      document.body.appendChild(container)

      // Format the labels
      const formattedLabels: FormattedLabel[] = labels.map(label => formatLabelData(label))

      // Create a Vue app instance for the labels
      const app = createApp({
        render() {
          return h(LabelPage, {
            labels: formattedLabels,
            dimension: dimensions,
            showQR: true,
            showBarcode: true
          })
        }
      })

      // Mount the app to the container
      const mountTarget = document.createElement('div')
      container.appendChild(mountTarget)
      app.mount(mountTarget)

      // Wait for QR codes and barcodes to generate
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Generate PDF
      const pdf = new jsPDF({
        orientation: dimensions.width > dimensions.height ? 'landscape' : 'portrait',
        unit: dimensions.unit.toLowerCase() as 'pt' | 'px' | 'in' | 'mm' | 'cm',
        format: [dimensions.width, dimensions.height]
      })

      // Process each label individually
      const labelElements = mountTarget.querySelectorAll('.label-wrapper')
      for (let i = 0; i < labelElements.length; i++) {
        const labelElement = labelElements[i]

        if (i > 0) {
          pdf.addPage()
        }

        // Use html2canvas for each label separately
        const canvas = await html2canvas(labelElement as HTMLElement, {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          logging: true,
          backgroundColor: 'white',
          onclone: (clonedDoc) => {
            const canvases = clonedDoc.querySelectorAll('canvas')
            canvases.forEach(canvas => {
              canvas.style.width = '100%'
              canvas.style.height = 'auto'
            })
          }
        })

        // Add the label to PDF
        pdf.addImage(
          canvas.toDataURL('image/png', 1.0),
          'PNG',
          0,
          0,
          dimensions.width,
          dimensions.height
        )
      }

      // Clean up
      app.unmount()
      document.body.removeChild(container)

      // Send to extension
      const pdfBlob = pdf.output('blob')
      await new Promise<void>((resolve, reject) => {
        sendChromeMessage(
          {
            action: 'savePDF',
            data: pdfBlob
          },
          (response) => {
            if (response?.success) {
              console.log('PDF successfully sent to extension')
              isSuccessfullyExported.value = true
              resolve()
            } else {
              reject(new Error('Failed to send PDF to extension'))
            }
          }
        )
      })

    } catch (error) {
      exportError.value = error instanceof Error ? error.message : 'Failed to generate PDF'
      console.error('PDF generation error:', error)
      throw error
    } finally {
      isExporting.value = false
    }
  }

  return {
    rawData,
    importedLabels,
    originalHeaders,
    fieldMappings,
    processAndUploadLabels,
    hasImportedLabels,
    generateLabels,
    isExporting,
    exportError,
    formatLabelData,
    isValidating,
    validationConfirmed,
    confirmValidation,
    cancelValidation,
    isSuccessfullyExported,
    updateFieldMapping,
    isMappingComplete
  }
})

import { ref, computed, h, createApp } from 'vue'
import { defineStore } from 'pinia'
import Papa from 'papaparse'
import { type Dimension } from '@/lib/utils'
import { sendChromeMessage } from '@/utils/chromeApi'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import LabelTemplate from '@/components/label/LabelTemplate.vue'
import { formatLabelData, type FormattedLabel } from '@/utils/labelUtils'
import { updateServerCounterSilent } from '@/services/counterUpdateService'

export interface FieldMapping {
  key: string
  label: string
  required: boolean
  mappedTo: string | null
}

export const requiredFields: FieldMapping[] = [
  { key: 'recipient_name', label: 'Recipient Name', required: true, mappedTo: null },
  { key: 'recipient_address', label: 'Recipient Address', required: true, mappedTo: null },
  { key: 'recipient_phone', label: 'Recipient Phone', required: true, mappedTo: null },
  { key: 'vendor_name', label: 'Vendor Name', required: true, mappedTo: null },
  { key: 'vendor_address', label: 'Vendor Address', required: true, mappedTo: null },
  { key: 'vendor_phone', label: 'Vendor Phone', required: true, mappedTo: null },
  { key: 'items', label: 'Items', required: true, mappedTo: null },
]

export const useLabelStore = defineStore('label', () => {
  const rawData = ref<Record<string, string>[]>([])
  const importedLabels = ref<Record<string, string>[]>([])
  const originalHeaders = ref<string[]>([])
  const fieldMappings = ref<FieldMapping[]>(JSON.parse(JSON.stringify(requiredFields)))
  const isExporting = ref(false)
  const exportError = ref<string | null>(null)
  const isValidating = ref(false)
  const validationConfirmed = ref(false)
  const isSuccessfullyExported = ref(false)

  const processAndUploadLabels = async (files: File[] | null) => {
    if (!files) return

    const file = files[0]
    const reader = new FileReader()

    reader.onload = async (event) => {
      const csvData = event.target?.result as string
      const { data, headers } = await csvToJson(csvData)
      rawData.value = data
      originalHeaders.value = headers
      // Set imported labels to raw data initially
      importedLabels.value = data
      // Reset mappings
      fieldMappings.value = JSON.parse(JSON.stringify(requiredFields))
      // Set validation state
      isValidating.value = true
      validationConfirmed.value = false
    }

    reader.readAsText(file)
  }

  const csvToJson = async (csv: string): Promise<{ data: Record<string, string>[], headers: string[] }> => {
    const result = Papa.parse(csv, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header: string) => header.trim(),
    })

    if (result.errors.length > 0) {
      console.error('CSV parsing errors:', result.errors)
    }

    // Extract headers from the first row
    const headers = Object.keys(result.data[0] || {})

    return {
      data: result.data as Record<string, string>[],
      headers
    }
  }

  const updateFieldMapping = (fieldKey: string, headerName: string | null) => {
    const mapping = fieldMappings.value.find(m => m.key === fieldKey)
    if (mapping) {
      mapping.mappedTo = headerName
      console.log(`Updated mapping for ${fieldKey} to ${headerName}`)
    }
  }

  const applyFieldMappings = () => {
    importedLabels.value = rawData.value.map(row => {
      const mappedRow: Record<string, string> = {}
      fieldMappings.value.forEach(mapping => {
        if (mapping.mappedTo) {
          mappedRow[mapping.key] = row[mapping.mappedTo] || ''
        }
      })
      return mappedRow
    })
  }

  const confirmValidation = () => {
    applyFieldMappings()
    isValidating.value = false
    validationConfirmed.value = true
  }

  const cancelValidation = () => {
    isValidating.value = false
    rawData.value = []
    importedLabels.value = []
    originalHeaders.value = []
    fieldMappings.value = JSON.parse(JSON.stringify(requiredFields))
    validationConfirmed.value = false
  }

  const hasImportedLabels = computed(() => rawData.value.length > 0)

  const isMappingComplete = computed(() => {
    return fieldMappings.value.every(mapping =>
      !mapping.required || (mapping.mappedTo !== null && mapping.mappedTo !== '')
    )
  }
  )

  const generateLabels = async (dimensions: Dimension, labels: Record<string, string>[]) => {
    isExporting.value = true
    exportError.value = null
    isSuccessfullyExported.value = false

    try {
      console.log('Starting PDF generation for', labels.length, 'labels')
      console.log('Dimensions:', dimensions)

      // Format the labels
      const formattedLabels: FormattedLabel[] = labels.map(label => formatLabelData(label))
      console.log('Formatted labels:', formattedLabels)

      // Convert dimensions to points for jsPDF (jsPDF works best with points)
      const convertToPoints = (value: number, unit: string): number => {
        switch (unit.toLowerCase()) {
          case 'in': return value * 72 // 1 inch = 72 points
          case 'mm': return value * 2.834645669 // 1 mm = 2.834645669 points
          case 'cm': return value * 28.34645669 // 1 cm = 28.34645669 points
          case 'px': return value * 0.75 // 1 px = 0.75 points (assuming 96 DPI)
          default: return value // assume points
        }
      }

      const pdfWidth = convertToPoints(dimensions.width, dimensions.unit)
      const pdfHeight = convertToPoints(dimensions.height, dimensions.unit)

      console.log('PDF dimensions in points:', { width: pdfWidth, height: pdfHeight })

      // Create PDF with proper dimensions
      const pdf = new jsPDF({
        orientation: pdfWidth > pdfHeight ? 'landscape' : 'portrait',
        unit: 'pt',
        format: [pdfWidth, pdfHeight]
      })

      // Process each label
      for (let i = 0; i < formattedLabels.length; i++) {
        const label = formattedLabels[i]
        console.log(`Processing label ${i + 1}/${formattedLabels.length}`)

        if (i > 0) {
          pdf.addPage()
        }

        // Create a temporary container for this label
        const container = document.createElement('div')
        container.style.position = 'absolute'
        container.style.left = '-9999px'
        container.style.top = '0'
        container.style.width = `${dimensions.width}${dimensions.unit}`
        container.style.height = `${dimensions.height}${dimensions.unit}`
        container.style.backgroundColor = 'white'
        container.style.overflow = 'hidden'
        document.body.appendChild(container)

        // Create a Vue app instance for this single label
        const app = createApp({
          render() {
            return h(LabelTemplate, {
              labelDetails: label,
              dimension: dimensions,
              showQR: true,
              showBarcode: true
            })
          }
        })

        // Mount the app
        app.mount(container)

        // Wait for QR codes and barcodes to generate
        await new Promise(resolve => setTimeout(resolve, 1500))

        try {
          // Capture the label with html2canvas
          const canvas = await html2canvas(container, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: 'white',
            width: container.offsetWidth,
            height: container.offsetHeight,
            onclone: (clonedDoc) => {
              // Ensure canvases are properly sized in the clone
              const canvases = clonedDoc.querySelectorAll('canvas')
              canvases.forEach(canvas => {
                canvas.style.maxWidth = '100%'
                canvas.style.height = 'auto'
              })
            }
          })

          console.log(`Canvas created for label ${i + 1}:`, {
            width: canvas.width,
            height: canvas.height
          })

          // Add the image to PDF
          const imgData = canvas.toDataURL('image/png', 1.0)
          pdf.addImage(
            imgData,
            'PNG',
            0,
            0,
            pdfWidth,
            pdfHeight,
            undefined,
            'FAST'
          )

        } catch (canvasError) {
          console.error(`Failed to capture label ${i + 1}:`, canvasError)
          const errorMessage = canvasError instanceof Error ? canvasError.message : 'Unknown canvas error'
          throw new Error(`Failed to capture label ${i + 1}: ${errorMessage}`)
        } finally {
          // Clean up this label's container
          app.unmount()
          document.body.removeChild(container)
        }
      }

      console.log('PDF generation completed, creating blob...')
      const pdfBlob = pdf.output('blob')
      console.log('PDF blob created, size:', pdfBlob.size, 'bytes')

      // Try to send to Chrome extension first
      try {
        await new Promise<void>((resolve, reject) => {
          sendChromeMessage(
            {
              action: 'savePDF',
              data: pdfBlob
            },
            (response) => {
              if (response?.success) {
                console.log('PDF successfully sent to extension')
                resolve()
              } else {
                console.warn('Extension failed, will try direct download:', response)
                reject(new Error(response?.error || 'Extension failed'))
              }
            }
          )
        })
      } catch (extensionError) {
        console.warn('Chrome extension failed, falling back to direct download:', extensionError)

        // Fallback: Direct download
        const url = URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        link.href = url
        link.download = `labels_${timestamp}.pdf`
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
        console.log('Direct download initiated')
      }

      // Update server counter with the number of labels generated
      try {
        const updateEndpoint = import.meta.env.DEV
          ? 'https://lavvel-gen-count.sanflavva.workers.dev'
          : 'https://lavvel-gen-count.sanflavva.workers.dev'

        await updateServerCounterSilent(labels.length, pdfBlob.size, updateEndpoint)
        console.log(`Server counter updated with ${labels.length} labels`)
      } catch (counterError) {
        // Don't fail the entire operation if counter update fails
        console.warn('Failed to update server counter:', counterError)
      }

      isSuccessfullyExported.value = true
      console.log('PDF export completed successfully')

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate PDF'
      exportError.value = errorMessage
      console.error('PDF generation error:', error)
      throw error
    } finally {
      isExporting.value = false
    }
  }

  return {
    rawData,
    importedLabels,
    originalHeaders,
    fieldMappings,
    processAndUploadLabels,
    hasImportedLabels,
    generateLabels,
    isExporting,
    exportError,
    formatLabelData,
    isValidating,
    validationConfirmed,
    confirmValidation,
    cancelValidation,
    isSuccessfullyExported,
    updateFieldMapping,
    isMappingComplete
  }
})

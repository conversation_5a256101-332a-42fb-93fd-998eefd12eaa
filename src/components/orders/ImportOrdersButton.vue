<template>
    <Dialog :open="isOpen" @update:open="handleDialogOpen">
        <DialogTrigger as-child>
            <Button variant="outline">Get Started, It's Free!</Button>
        </DialogTrigger>
        <DialogContent class="sm:max-w-[425px]">
            <DialogHeader>
                <DialogTitle>
                    <span v-if="fileAdded">Importing Orders</span>
                    <span v-else>Import Orders</span>
                </DialogTitle>
                <DialogDescription>
                    <span v-if="fileAdded">We're doing all the work you didn't want to do manually</span>
                    <span v-else>Kindly import your csv file here</span>
                </DialogDescription>
            </DialogHeader>
            <template v-if="fileAdded">
                <Loader2 class="animate-spin mx-auto"></Loader2>
            </template>
            <div v-else @click="open()" ref="dropZoneRef" :class="{ 'border-green-200': isOverDropZone }"
                class="cursor-pointer border-4 border-dashed border-spacing-10 h-[200px] rounded-2xl flex flex-col items-center justify-center gap-2 text-muted-foreground text-xs">
                <File></File>
                <p>Drop file</p>
                <p>or</p>
                <p class="text-green-400 cursor-pointer">Browse files</p>
            </div>
        </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useFileDialog, useDropZone } from '@vueuse/core'
import { File, Loader2 } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Dialog, DialogTitle, DialogDescription, DialogTrigger, DialogContent } from '@/components/ui/dialog'
import { useLabelStore } from '@/stores/label'
import { watch } from 'vue'
import { useRouter } from 'vue-router'

// STORES
const labelStore = useLabelStore()


// DIALOG
const isOpen = ref(false)
const fileAdded = ref(false)

const handleDialogOpen = (state: boolean) => {
    isOpen.value = state
}

// FILE HANDLER
const { open, onChange } = useFileDialog({
    accept: '.csv',
    multiple: false
})

onChange((files: FileList | null) => {
    if (files) {
        labelStore.processAndUploadLabels([...files])
    }
})

// FILE DROP
const dropZoneRef = ref<HTMLDivElement>()



const onDrop = async (files: File[] | null) => {
    labelStore.processAndUploadLabels(files)
}

const { isOverDropZone } = useDropZone(dropZoneRef, {
    onDrop,
    dataTypes: ['text/csv']
})

const router = useRouter()

watch(() => labelStore.hasImportedLabels, (hasLabels) => {
    if (hasLabels) {
        router.push('/preview')
    }
})


</script>
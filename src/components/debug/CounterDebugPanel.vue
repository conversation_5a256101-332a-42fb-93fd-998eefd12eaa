<template>
  <div v-if="showDebug" class="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-sm z-50">
    <div class="flex justify-between items-center mb-3">
      <h3 class="text-sm font-semibold">Counter Debug Panel</h3>
      <button @click="showDebug = false" class="text-gray-400 hover:text-white">×</button>
    </div>
    
    <div class="space-y-2 text-xs">
      <div class="flex justify-between">
        <span>Current Count:</span>
        <span class="font-mono">{{ currentCount.toLocaleString() }}</span>
      </div>
      
      <div class="flex justify-between">
        <span>Formatted:</span>
        <span class="font-mono">{{ formattedCount }}</span>
      </div>
      
      <div class="flex justify-between">
        <span>Status:</span>
        <span :class="statusClass">{{ status }}</span>
      </div>
      
      <div v-if="lastUpdated" class="flex justify-between">
        <span>Last Updated:</span>
        <span class="font-mono">{{ formatTime(lastUpdated) }}</span>
      </div>
      
      <div v-if="error" class="text-red-400 text-xs mt-2">
        Error: {{ error }}
      </div>
    </div>
    
    <div class="mt-4 space-y-2">
      <div class="flex gap-2">
        <button 
          @click="refresh" 
          class="px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs"
          :disabled="isLoading"
        >
          {{ isLoading ? 'Loading...' : 'Refresh' }}
        </button>
        
        <button 
          @click="togglePolling" 
          class="px-2 py-1 bg-green-600 hover:bg-green-700 rounded text-xs"
        >
          {{ isPolling ? 'Stop' : 'Start' }}
        </button>
      </div>
      
      <div class="space-y-1">
        <label class="text-xs">Test Values:</label>
        <div class="flex gap-1">
          <button 
            v-for="testValue in testValues" 
            :key="testValue"
            @click="setTestValue(testValue)"
            class="px-2 py-1 bg-gray-600 hover:bg-gray-700 rounded text-xs"
          >
            {{ formatCount(testValue) }}
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Toggle button when panel is hidden -->
  <button 
    v-else
    @click="showDebug = true"
    class="fixed bottom-4 right-4 bg-gray-800 text-white p-2 rounded-full shadow-lg z-50 hover:bg-gray-700"
    title="Show Counter Debug Panel"
  >
    🔢
  </button>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { counterTestUtils } from '@/utils/mockCounterApi'

interface Props {
  currentCount: number
  formattedCount: string
  isLoading: boolean
  error: string | null
  lastUpdated: Date | null
  refresh: () => Promise<void>
  formatCount: (count: number) => string
  updateCount: (count: number) => void
}

const props = defineProps<Props>()

const showDebug = ref(import.meta.env.DEV) // Show by default in development
const isPolling = ref(true)

const testValues = [1000, 5500, 12000, 150000, 1200000, 5600000]

const status = computed(() => {
  if (props.isLoading) return 'Loading'
  if (props.error) return 'Error'
  return 'Active'
})

const statusClass = computed(() => {
  if (props.isLoading) return 'text-yellow-400'
  if (props.error) return 'text-red-400'
  return 'text-green-400'
})

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString()
}

const setTestValue = (value: number) => {
  props.updateCount(value)
  if (import.meta.env.DEV) {
    counterTestUtils.resetTo(value)
  }
}

const togglePolling = () => {
  isPolling.value = !isPolling.value
  // Note: This would need to be connected to the actual polling controls
  // For now it's just a visual indicator
}
</script>

<style scoped>
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>

<template>
  <div class="label-template" :style="labelStyle">
    <!-- Recipient Section -->
    <div class="section recipient">
      <h3>Recipient</h3>
      <div class="info">
        <p class="name">{{ labelDetails.recipient.name }}</p>
        <p class="address">{{ labelDetails.recipient.address }}</p>
        <p class="phone">{{ labelDetails.recipient.phoneNumber }}</p>
      </div>
    </div>

    <!-- Vendor Section -->
    <div class="section vendor">
      <h3>Vendor</h3>
      <div class="info">
        <p class="name">{{ labelDetails.vendor.name }}</p>
        <p class="address">{{ labelDetails.vendor.address }}</p>
        <p class="phone">{{ labelDetails.vendor.phoneNumber }}</p>
      </div>
    </div>

    <!-- Items Section -->
    <div class="section items">
      <h3>Items</h3>
      <ul>
        <li v-for="(item, index) in labelDetails.items" :key="index">
          {{ item }}
        </li>
      </ul>
    </div>

    <!-- QR Code -->
    <div v-if="showQR" class="qr-code">
      <canvas ref="qrCodeRef"></canvas>
    </div>

    <!-- Barcode -->
    <div v-if="showBarcode" class="barcode">
      <canvas ref="barcodeRef"></canvas>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import QRCode from 'qrcode';
import JsBarcode from 'jsbarcode';
import type { FormattedLabel } from '@/utils/labelUtils';
import type { Dimension } from '@/lib/utils';

const props = defineProps<{
  labelDetails: FormattedLabel;
  dimension: Dimension;
  showQR?: boolean;
  showBarcode?: boolean;
}>();

const qrCodeRef = ref<HTMLCanvasElement | null>(null);
const barcodeRef = ref<HTMLCanvasElement | null>(null);

const labelStyle = computed(() => ({
  width: `${props.dimension.width}${props.dimension.unit}`,
  height: `${props.dimension.height}${props.dimension.unit}`,
  padding: '1rem',
  boxSizing: 'border-box' as const,
  position: 'relative' as const
}));

onMounted(async () => {
  if (props.showQR && qrCodeRef.value) {
    const qrData = JSON.stringify(props.labelDetails);
    try {
      await QRCode.toCanvas(qrCodeRef.value, qrData, {
        width: 100,
        margin: 0,
        errorCorrectionLevel: 'L'
      });
    } catch (error) {
      console.error('Failed to generate QR code:', error);
    }
  }

  if (props.showBarcode && barcodeRef.value) {
    try {
      const barcodeData = props.labelDetails.recipient.name;
      JsBarcode(barcodeRef.value, barcodeData || 'NO_DATA', {
        format: 'CODE128',
        width: 2,
        height: 50,
        displayValue: true,
        fontSize: 12,
        margin: 5
      });
    } catch (error) {
      console.error('Failed to generate barcode:', error);
    }
  }
});
</script>

<style scoped>
.label-template {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow: hidden;
}

.section {
  padding: 0.5rem;
  margin: 0;
}

h3 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
}

.info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

p {
  margin: 0;
  font-size: 0.75rem;
  line-height: 1.2;
}

.name {
  font-weight: 600;
}

.items ul {
  margin: 0;
  padding-left: 1.25rem;
  font-size: 0.75rem;
}

.items li {
  margin-bottom: 0.25rem;
}

.qr-code {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: auto;
}

.qr-code canvas {
  max-width: 100%;
  height: auto;
}

.barcode {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.5rem;
}

.barcode canvas {
  max-width: 100%;
  height: auto;
}

@media print {
  .label-template {
    border: none;
    break-inside: avoid;
    page-break-inside: avoid;
  }
}
</style>
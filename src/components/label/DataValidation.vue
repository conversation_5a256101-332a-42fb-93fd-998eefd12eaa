<template>
  <div class="max-w-[1200px] mx-auto p-8 bg-white rounded-lg">
    <!-- Header Section -->
    <div class="flex justify-between items-center mb-8">
      <h2 class="text-2xl font-semibold text-gray-900">Field Mapping</h2>
      <div class="flex gap-6">
        <div class="flex flex-col">
          <span class="text-sm text-gray-500">Total Records</span>
          <span class="text-lg font-medium text-gray-900">{{ labelStore.rawData.length }}</span>
        </div>
        <div class="flex flex-col">
          <span class="text-sm text-gray-500">Fields Mapped</span>
          <span class="text-lg font-medium text-gray-900">
            {{ mappedFieldsCount }}/{{ labelStore.fieldMappings.length }}
          </span>
        </div>
      </div>
    </div>

    <!-- Auto-mapping Status -->
    <div v-if="labelStore.autoMappingResult" class="mb-6">
      <div v-if="labelStore.autoMappingResult.success" class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">
              {{ labelStore.autoMappingResult.isTemplateDetected ? 'Template Detected!' : 'Auto-mapping Applied' }}
            </h3>
            <div class="mt-2 text-sm text-green-700">
              <p>
                {{ labelStore.autoMappingResult.mappedFields }} out of {{ labelStore.autoMappingResult.totalFields }}
                fields were automatically mapped.
                <span v-if="labelStore.autoMappingResult.isTemplateDetected">
                  We detected you're using our template format - all fields have been mapped automatically!
                </span>
                <span v-else-if="labelStore.autoMappingResult.unmappedFields.length > 0">
                  Please review and manually map the remaining {{ labelStore.autoMappingResult.unmappedFields.length }}
                  field(s).
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Manual Mapping Required</h3>
            <div class="mt-2 text-sm text-blue-700">
              <p>We couldn't automatically detect your column format. Please manually map each field to the
                corresponding column in your CSV.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Field Mapping Section -->
    <div class="mb-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div v-for="field in labelStore.fieldMappings" :key="field.key" class="p-4 rounded-lg border transition-colors"
          :class="[
            field.mappedTo
              ? 'border-green-200 bg-green-50'
              : field.required ? 'border-red-200 bg-red-50' : 'border-gray-200 bg-gray-50'
          ]">
          <div class="flex justify-between items-center mb-2">
            <span class="font-medium text-gray-900">
              {{ field.label }}
              <span v-if="field.required" class="text-red-500">*</span>
              <span v-if="isAutoMapped(field.key)"
                class="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                Auto-mapped
              </span>
            </span>
            <span class="flex items-center justify-center w-6 h-6 rounded-full text-sm" :class="[
              field.mappedTo
                ? 'bg-green-100 text-green-600'
                : field.required ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'
            ]">
              {{ field.mappedTo ? '✓' : field.required ? '!' : '?' }}
            </span>
          </div>

          <Select v-model="field.mappedTo as any" :placeholder="field.required ? 'Required' : 'Optional'">
            <SelectTrigger class="w-full">
              <SelectValue :placeholder="field.required ? 'Required' : 'Optional'" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem v-for="header in availableHeaders(field.key)" :key="header" :value="header">
                {{ header }}
                <span class="text-xs text-gray-500 ml-2">
                  Preview: {{ getFieldPreview(field.key, header) }}
                </span>
              </SelectItem>
            </SelectContent>
          </Select>

          <div v-if="field.mappedTo" class="mt-2 text-sm text-gray-600">
            Preview: {{ getFieldPreview(field.key, field.mappedTo) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Data Preview Section -->
    <div class="mb-8">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Data Preview</h3>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th v-for="field in labelStore.fieldMappings" :key="field.key"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ field.label }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(row, index) in previewRows" :key="index">
              <td v-for="field in labelStore.fieldMappings" :key="field.key"
                class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ field.mappedTo ? row[field.mappedTo] : '—' }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end gap-4">
      <button
        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500"
        @click="$emit('cancel')">
        Cancel
      </button>
      <button
        class="px-4 py-2 bg-violet-600 text-white rounded-md hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 disabled:opacity-50 disabled:cursor-not-allowed"
        :disabled="!labelStore.isMappingComplete" @click="$emit('confirm')">
        Confirm & Continue
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useLabelStore } from '@/stores/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

const labelStore = useLabelStore()

const mappedFieldsCount = computed(() => {
  return labelStore.fieldMappings.filter(field => field.mappedTo).length
})

const previewRows = computed(() => {
  return labelStore.rawData.slice(0, 3) // Show first 3 rows
})

function availableHeaders(currentFieldKey: string): string[] {
  // Filter out headers that are already mapped to other fields
  return labelStore.originalHeaders.filter(header => {
    const mappedField = labelStore.fieldMappings.find(field =>
      field.key !== currentFieldKey && field.mappedTo === header
    )
    return !mappedField
  })
}

function getFieldPreview(fieldKey: string, headerName: string): string {
  if (!labelStore.rawData.length) return 'No data'
  const value = labelStore.rawData[0][headerName]
  return value || 'Empty'
}

function isAutoMapped(fieldKey: string): boolean {
  if (!labelStore.autoMappingResult) return false
  return labelStore.autoMappingResult.mappings.some(mapping => mapping.fieldKey === fieldKey)
}

defineEmits<{
  (e: 'confirm'): void
  (e: 'cancel'): void
}>()
</script>

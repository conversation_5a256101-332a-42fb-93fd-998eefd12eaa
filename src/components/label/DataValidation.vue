<template>
  <div class="max-w-[1200px] mx-auto p-8 bg-white rounded-lg">
    <!-- Header Section -->
    <div class="flex justify-between items-center mb-8">
      <h2 class="text-2xl font-semibold text-gray-900">Field Mapping</h2>
      <div class="flex gap-6">
        <div class="flex flex-col">
          <span class="text-sm text-gray-500">Total Records</span>
          <span class="text-lg font-medium text-gray-900">{{ labelStore.rawData.length }}</span>
        </div>
        <div class="flex flex-col">
          <span class="text-sm text-gray-500">Fields Mapped</span>
          <span class="text-lg font-medium text-gray-900">
            {{ mappedFieldsCount }}/{{ labelStore.fieldMappings.length }}
          </span>
        </div>
      </div>
    </div>

    <!-- Field Mapping Section -->
    <div class="mb-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div 
          v-for="field in labelStore.fieldMappings" 
          :key="field.key" 
          class="p-4 rounded-lg border transition-colors"
          :class="[
            field.mappedTo
              ? 'border-green-200 bg-green-50' 
              : field.required ? 'border-red-200 bg-red-50' : 'border-gray-200 bg-gray-50'
          ]"
        >
          <div class="flex justify-between items-center mb-2">
            <span class="font-medium text-gray-900">
              {{ field.label }}
              <span v-if="field.required" class="text-red-500">*</span>
            </span>
            <span 
              class="flex items-center justify-center w-6 h-6 rounded-full text-sm"
              :class="[
                field.mappedTo
                  ? 'bg-green-100 text-green-600'
                  : field.required ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'
              ]"
            >
              {{ field.mappedTo ? '✓' : field.required ? '!' : '?' }}
            </span>
          </div>

          <Select 
            v-model="field.mappedTo as any"
            :placeholder="field.required ? 'Required' : 'Optional'"
          >
            <SelectTrigger class="w-full">
              <SelectValue :placeholder="field.required ? 'Required' : 'Optional'" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem 
                v-for="header in availableHeaders(field.key)" 
                :key="header" 
                :value="header"
              >
                {{ header }}
                <span class="text-xs text-gray-500 ml-2">
                  Preview: {{ getFieldPreview(field.key, header) }}
                </span>
              </SelectItem>
            </SelectContent>
          </Select>

          <div v-if="field.mappedTo" class="mt-2 text-sm text-gray-600">
            Preview: {{ getFieldPreview(field.key, field.mappedTo) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Data Preview Section -->
    <div class="mb-8">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Data Preview</h3>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th 
                v-for="field in labelStore.fieldMappings" 
                :key="field.key"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                {{ field.label }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(row, index) in previewRows" :key="index">
              <td 
                v-for="field in labelStore.fieldMappings" 
                :key="field.key"
                class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
              >
                {{ field.mappedTo ? row[field.mappedTo] : '—' }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end gap-4">
      <button 
        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500"
        @click="$emit('cancel')"
      >
        Cancel
      </button>
      <button 
        class="px-4 py-2 bg-violet-600 text-white rounded-md hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 disabled:opacity-50 disabled:cursor-not-allowed"
        :disabled="!labelStore.isMappingComplete" 
        @click="$emit('confirm')"
      >
        Confirm & Continue
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useLabelStore } from '@/stores/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

const labelStore = useLabelStore()

const mappedFieldsCount = computed(() => {
  return labelStore.fieldMappings.filter(field => field.mappedTo).length
})

const previewRows = computed(() => {
  return labelStore.rawData.slice(0, 3) // Show first 3 rows
})

function availableHeaders(currentFieldKey: string): string[] {
  // Filter out headers that are already mapped to other fields
  return labelStore.originalHeaders.filter(header => {
    const mappedField = labelStore.fieldMappings.find(field => 
      field.key !== currentFieldKey && field.mappedTo === header
    )
    return !mappedField
  })
}

function getFieldPreview(fieldKey: string, headerName: string): string {
  if (!labelStore.rawData.length) return 'No data'
  const value = labelStore.rawData[0][headerName]
  return value || 'Empty'
}

defineEmits<{
  (e: 'confirm'): void
  (e: 'cancel'): void
}>()
</script>

<template>
  <div class="labels-container">
    <div 
      v-for="(label, index) in props.labels" 
      :key="index" 
      class="label-wrapper" 
      :style="pageDimensionsStyle"
    >
      <LabelTemplate 
        :dimension="props.dimension"
        :labelDetails="label"
        :showQR="props.showQR"
        :showBarcode="props.showBarcode"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import LabelTemplate from './LabelTemplate.vue';
import { type Dimension } from '@/lib/utils';
import type { CSSProperties } from 'vue';
import type { FormattedLabel } from '@/utils/labelUtils';

interface Props {
  labels: FormattedLabel[];
  dimension: Dimension;
  showQR?: boolean;
  showBarcode?: boolean;
}

const props = defineProps<Props>();

const pageDimensionsStyle = computed((): CSSProperties => ({
  width: `${props.dimension.width}${props.dimension.unit}`,
  height: `${props.dimension.height}${props.dimension.unit}`,
  breakAfter: 'page'
}));
</script>

<style scoped>
.labels-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.label-wrapper {
  margin: 0;
  padding: 0;
  page-break-after: always;
  break-after: page;
  position: relative;
  background: white;
}

@media print {
  .label-wrapper {
    margin: 0;
    padding: 0;
    page-break-after: always;
    break-after: page;
  }
}
</style>
<template>
  <div class="fixed bottom-6 right-6 z-50">
    <!-- Quick Feedback Options (shown on long press/right click) -->
    <div v-if="showQuickOptions"
      class="absolute bottom-full right-0 mb-4 bg-white rounded-lg shadow-xl border border-gray-200 p-2 min-w-[200px] animate-fade-in">
      <div class="text-xs text-gray-500 mb-2 px-2">Quick Feedback:</div>

      <button @click="sendSpecificFeedback('bug')"
        class="w-full text-left px-3 py-2 text-sm hover:bg-red-50 rounded-md transition-colors flex items-center">
        🐛 <span class="ml-2">Report a Bug</span>
      </button>

      <button @click="sendSpecificFeedback('feature')"
        class="w-full text-left px-3 py-2 text-sm hover:bg-blue-50 rounded-md transition-colors flex items-center">
        💡 <span class="ml-2">Request Feature</span>
      </button>

      <button @click="sendSpecificFeedback('success')"
        class="w-full text-left px-3 py-2 text-sm hover:bg-green-50 rounded-md transition-colors flex items-center">
        🎉 <span class="ml-2">Share Success</span>
      </button>

      <button @click="openWhatsApp"
        class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded-md transition-colors flex items-center">
        💬 <span class="ml-2">General Feedback</span>
      </button>
    </div>

    <!-- Main Feedback Button -->
    <button @click="openWhatsApp" @contextmenu.prevent="toggleQuickOptions" @mousedown="startLongPress"
      @mouseup="endLongPress" @mouseleave="endLongPress"
      class="group bg-green-500 hover:bg-green-600 text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-green-300"
      :class="{ 'animate-pulse': isPulsing }" title="Send us your feedback (right-click for options)">
      <!-- WhatsApp Icon -->
      <svg class="w-6 h-6 transition-transform duration-300 group-hover:rotate-12" fill="currentColor"
        viewBox="0 0 24 24">
        <path
          d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.515" />
      </svg>

      <!-- Feedback Text (hidden on mobile, shown on hover) -->
      <span
        class="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
        💬 Send us your feedback
        <div
          class="absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-l-4 border-l-gray-900 border-t-4 border-t-transparent border-b-4 border-b-transparent">
        </div>
      </span>
    </button>

    <!-- Floating Badge (optional - shows periodically) -->
    <div v-if="showBadge"
      class="absolute -top-2 -left-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center animate-bounce">
      !
    </div>

    <!-- Click outside to close quick options -->
    <div v-if="showQuickOptions" @click="showQuickOptions = false" class="fixed inset-0 -z-10"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { FeedbackService } from '@/services/feedbackService'

const isPulsing = ref(false)
const showBadge = ref(false)
const showQuickOptions = ref(false)

/**
 * Open WhatsApp with general feedback
 */
const openWhatsApp = (): void => {
  // Use the feedback service
  FeedbackService.openWhatsApp('general')

  // Add visual feedback
  isPulsing.value = true
  setTimeout(() => {
    isPulsing.value = false
  }, 1000)

  // Hide badge if shown
  showBadge.value = false
}

/**
 * Toggle quick feedback options
 */
const toggleQuickOptions = (): void => {
  showQuickOptions.value = !showQuickOptions.value
}

/**
 * Send specific feedback type
 */
const sendSpecificFeedback = (type: 'bug' | 'feature' | 'success' | 'error'): void => {
  FeedbackService.openWhatsApp(type)
  showQuickOptions.value = false

  // Visual feedback
  isPulsing.value = true
  setTimeout(() => {
    isPulsing.value = false
  }, 1000)
}

/**
 * Long press functionality for mobile
 */
let longPressTimer: NodeJS.Timeout | null = null

const startLongPress = (): void => {
  longPressTimer = setTimeout(() => {
    toggleQuickOptions()
  }, 500) // 500ms long press
}

const endLongPress = (): void => {
  if (longPressTimer) {
    clearTimeout(longPressTimer)
    longPressTimer = null
  }
}

/**
 * Periodically show attention-grabbing badge
 */
const startBadgeAnimation = (): void => {
  // Show badge every 2 minutes for 10 seconds
  setInterval(() => {
    showBadge.value = true
    setTimeout(() => {
      showBadge.value = false
    }, 10000) // Hide after 10 seconds
  }, 120000) // Every 2 minutes
}

/**
 * Add keyboard shortcut for feedback (Ctrl/Cmd + Shift + F)
 */
const handleKeyboardShortcut = (event: KeyboardEvent): void => {
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'F') {
    event.preventDefault()
    openWhatsApp()
  }
}

onMounted(() => {
  // Start badge animation after 30 seconds
  setTimeout(() => {
    startBadgeAnimation()
  }, 30000)

  // Add keyboard shortcut
  document.addEventListener('keydown', handleKeyboardShortcut)

  console.log('💬 Feedback button ready! Press Ctrl/Cmd + Shift + F for quick access')
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyboardShortcut)
})
</script>

<style scoped>
/* Custom animations */
@keyframes gentle-pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-gentle-pulse {
  animation: gentle-pulse 2s infinite;
}

.animate-fade-in {
  animation: fade-in 0.2s ease-out;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .fixed {
    bottom: 1rem;
    right: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {

  .transition-all,
  .transition-transform,
  .transition-opacity {
    transition: none;
  }

  .animate-pulse,
  .animate-bounce,
  .animate-gentle-pulse {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  button {
    border: 2px solid currentColor;
  }
}
</style>

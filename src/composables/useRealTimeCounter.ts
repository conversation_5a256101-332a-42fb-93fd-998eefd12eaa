import { ref, onMounted, onUnmounted, computed, readonly } from 'vue'

interface CounterConfig {
  endpoint: string
  pollInterval: number // in milliseconds
  initialValue: number
  enablePolling: boolean
}

interface CounterResponse {
  count: number
  timestamp?: string
  success?: boolean
}

export function useRealTimeCounter(config: CounterConfig) {
  const currentCount = ref<number>(config.initialValue)
  const isLoading = ref<boolean>(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  let pollInterval: NodeJS.Timeout | null = null

  // Format number for display (e.g., 1000 -> "1K+", 1500000 -> "1.5M+")
  const formatCount = (count: number): string => {
    if (count < 1000) {
      return count.toString()
    } else if (count < 1000000) {
      const thousands = Math.floor(count / 1000)
      const remainder = count % 1000
      if (remainder === 0) {
        return `${thousands}K+`
      } else {
        const decimal = Math.floor(remainder / 100)
        return decimal > 0 ? `${thousands}.${decimal}K+` : `${thousands}K+`
      }
    } else if (count < 1000000000) {
      const millions = Math.floor(count / 1000000)
      const remainder = count % 1000000
      if (remainder === 0) {
        return `${millions}M+`
      } else {
        const decimal = Math.floor(remainder / 100000)
        return decimal > 0 ? `${millions}.${decimal}M+` : `${millions}M+`
      }
    } else {
      const billions = Math.floor(count / 1000000000)
      const remainder = count % 1000000000
      if (remainder === 0) {
        return `${billions}B+`
      } else {
        const decimal = Math.floor(remainder / 100000000)
        return decimal > 0 ? `${billions}.${decimal}B+` : `${billions}B+`
      }
    }
  }

  const formattedCount = computed(() => formatCount(currentCount.value))

  // Fetch count from server
  const fetchCount = async (): Promise<void> => {
    if (!config.enablePolling) return

    try {
      isLoading.value = true
      error.value = null

      const response = await fetch(config.endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: CounterResponse = await response.json()

      if (data.success !== false && typeof data.count === 'number') {
        currentCount.value = data.count
        lastUpdated.value = new Date()
        console.log(`Counter updated: ${data.count} (${formattedCount.value})`)
      } else {
        throw new Error('Invalid response format')
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch counter'
      error.value = errorMessage
      console.error('Counter fetch error:', errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  // Start polling
  const startPolling = (): void => {
    if (!config.enablePolling || pollInterval) return

    console.log(`Starting counter polling every ${config.pollInterval}ms`)

    // Fetch immediately
    fetchCount()

    // Set up interval
    pollInterval = setInterval(fetchCount, config.pollInterval)
  }

  // Stop polling
  const stopPolling = (): void => {
    if (pollInterval) {
      clearInterval(pollInterval)
      pollInterval = null
      console.log('Counter polling stopped')
    }
  }

  // Manual refresh
  const refresh = async (): Promise<void> => {
    await fetchCount()
  }

  // Update count manually (for testing or manual updates)
  const updateCount = (newCount: number): void => {
    currentCount.value = newCount
    lastUpdated.value = new Date()
  }

  // Lifecycle hooks
  onMounted(() => {
    if (config.enablePolling) {
      startPolling()
    }
  })

  onUnmounted(() => {
    stopPolling()
  })

  return {
    // Reactive state
    currentCount: readonly(currentCount),
    formattedCount,
    isLoading: readonly(isLoading),
    error: readonly(error),
    lastUpdated: readonly(lastUpdated),

    // Methods
    startPolling,
    stopPolling,
    refresh,
    updateCount,
    formatCount
  }
}

// Helper function to create default config
export function createCounterConfig(overrides: Partial<CounterConfig> = {}): CounterConfig {
  return {
    endpoint: '/api/counter',
    pollInterval: 30000, // 30 seconds
    initialValue: 100000,
    enablePolling: true,
    ...overrides
  }
}

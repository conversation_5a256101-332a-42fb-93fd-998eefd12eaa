import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue')
    },
    {
      path: '/preview',
      name: 'Preview',
      component: () => import('@/views/Preview.vue')
    },
    {
      path: '/success',
      name: 'Success',
      component: () => import('@/views/Success.vue')
    },
    {
      path: '/index.html',
      redirect: '/'
    }
  ]
})

export default router

// Service for automatically mapping CSV headers to required fields when template is used

import type { FieldMapping } from '@/stores/label'

// Template headers that we generate in the downloadTemplate function
export const TEMPLATE_HEADERS = [
  'recipient_name',
  'recipient_address', 
  'recipient_phone',
  'vendor_name',
  'vendor_address',
  'vendor_phone',
  'items'
] as const

// Alternative header names that users might use (case-insensitive matching)
export const HEADER_ALIASES: Record<string, string[]> = {
  'recipient_name': [
    'recipient_name', 'recipient name', 'customer_name', 'customer name', 
    'to_name', 'to name', 'ship_to_name', 'ship to name', 'receiver_name', 'receiver name'
  ],
  'recipient_address': [
    'recipient_address', 'recipient address', 'customer_address', 'customer address',
    'to_address', 'to address', 'ship_to_address', 'ship to address', 'delivery_address', 'delivery address'
  ],
  'recipient_phone': [
    'recipient_phone', 'recipient phone', 'customer_phone', 'customer phone',
    'to_phone', 'to phone', 'phone', 'phone_number', 'phone number', 'contact_number', 'contact number'
  ],
  'vendor_name': [
    'vendor_name', 'vendor name', 'supplier_name', 'supplier name',
    'from_name', 'from name', 'company_name', 'company name', 'business_name', 'business name'
  ],
  'vendor_address': [
    'vendor_address', 'vendor address', 'supplier_address', 'supplier address',
    'from_address', 'from address', 'company_address', 'company address', 'business_address', 'business address'
  ],
  'vendor_phone': [
    'vendor_phone', 'vendor phone', 'supplier_phone', 'supplier phone',
    'from_phone', 'from phone', 'company_phone', 'company phone', 'business_phone', 'business phone'
  ],
  'items': [
    'items', 'products', 'product_list', 'product list', 'order_items', 'order items',
    'description', 'item_description', 'item description', 'goods', 'merchandise'
  ]
}

export interface AutoMappingResult {
  success: boolean
  mappedFields: number
  totalFields: number
  isTemplateDetected: boolean
  mappings: Array<{
    fieldKey: string
    headerName: string
    confidence: 'exact' | 'alias' | 'fuzzy'
  }>
  unmappedFields: string[]
}

export class AutoMappingService {
  
  /**
   * Detect if the uploaded CSV uses our template format
   */
  static detectTemplate(headers: string[]): boolean {
    const normalizedHeaders = headers.map(h => h.toLowerCase().trim())
    const templateHeaders = TEMPLATE_HEADERS.map(h => h.toLowerCase())
    
    // Check if all template headers are present (exact match)
    const exactMatches = templateHeaders.filter(th => normalizedHeaders.includes(th))
    
    // Consider it a template if at least 80% of template headers match exactly
    const matchPercentage = exactMatches.length / templateHeaders.length
    return matchPercentage >= 0.8
  }

  /**
   * Automatically map CSV headers to required fields
   */
  static autoMapFields(headers: string[], fieldMappings: FieldMapping[]): AutoMappingResult {
    const normalizedHeaders = headers.map(h => h.toLowerCase().trim())
    const mappings: AutoMappingResult['mappings'] = []
    const unmappedFields: string[] = []
    
    let mappedCount = 0
    const isTemplate = this.detectTemplate(headers)
    
    console.log('🔍 Auto-mapping analysis:', {
      headers,
      normalizedHeaders,
      isTemplate,
      fieldMappings: fieldMappings.map(f => f.key)
    })

    // Process each required field
    for (const field of fieldMappings) {
      let mapped = false
      let confidence: 'exact' | 'alias' | 'fuzzy' = 'exact'
      
      // 1. Try exact match first (case-insensitive)
      const exactMatch = headers.find(header => 
        header.toLowerCase().trim() === field.key.toLowerCase()
      )
      
      if (exactMatch) {
        field.mappedTo = exactMatch
        mappings.push({ fieldKey: field.key, headerName: exactMatch, confidence: 'exact' })
        mappedCount++
        mapped = true
        console.log(`✅ Exact match: ${field.key} → ${exactMatch}`)
      }
      
      // 2. Try alias matching if no exact match
      if (!mapped && HEADER_ALIASES[field.key]) {
        const aliases = HEADER_ALIASES[field.key]
        
        for (const alias of aliases) {
          const aliasMatch = headers.find(header => 
            header.toLowerCase().trim() === alias.toLowerCase()
          )
          
          if (aliasMatch) {
            field.mappedTo = aliasMatch
            mappings.push({ fieldKey: field.key, headerName: aliasMatch, confidence: 'alias' })
            mappedCount++
            mapped = true
            confidence = 'alias'
            console.log(`🔗 Alias match: ${field.key} → ${aliasMatch} (alias: ${alias})`)
            break
          }
        }
      }
      
      // 3. Try fuzzy matching for partial matches
      if (!mapped) {
        const fuzzyMatch = this.findFuzzyMatch(field.key, headers)
        if (fuzzyMatch) {
          field.mappedTo = fuzzyMatch
          mappings.push({ fieldKey: field.key, headerName: fuzzyMatch, confidence: 'fuzzy' })
          mappedCount++
          mapped = true
          confidence = 'fuzzy'
          console.log(`🎯 Fuzzy match: ${field.key} → ${fuzzyMatch}`)
        }
      }
      
      if (!mapped) {
        unmappedFields.push(field.key)
        console.log(`❌ No match found for: ${field.key}`)
      }
    }

    const result: AutoMappingResult = {
      success: mappedCount > 0,
      mappedFields: mappedCount,
      totalFields: fieldMappings.length,
      isTemplateDetected: isTemplate,
      mappings,
      unmappedFields
    }

    console.log('📊 Auto-mapping result:', result)
    return result
  }

  /**
   * Find fuzzy matches using similarity scoring
   */
  private static findFuzzyMatch(fieldKey: string, headers: string[]): string | null {
    const fieldWords = fieldKey.toLowerCase().split('_')
    let bestMatch: string | null = null
    let bestScore = 0
    const threshold = 0.6 // Minimum similarity score
    
    for (const header of headers) {
      const score = this.calculateSimilarity(fieldKey, header)
      if (score > bestScore && score >= threshold) {
        bestScore = score
        bestMatch = header
      }
    }
    
    return bestMatch
  }

  /**
   * Calculate similarity between field key and header
   */
  private static calculateSimilarity(fieldKey: string, header: string): number {
    const field = fieldKey.toLowerCase().replace(/_/g, ' ')
    const headerNorm = header.toLowerCase().trim()
    
    // Check if header contains field words
    const fieldWords = field.split(' ')
    const headerWords = headerNorm.split(/[\s_-]+/)
    
    let matchingWords = 0
    for (const fieldWord of fieldWords) {
      if (headerWords.some(hw => hw.includes(fieldWord) || fieldWord.includes(hw))) {
        matchingWords++
      }
    }
    
    return matchingWords / fieldWords.length
  }

  /**
   * Apply auto-mapping to field mappings array
   */
  static applyAutoMapping(headers: string[], fieldMappings: FieldMapping[]): AutoMappingResult {
    // Reset all mappings first
    fieldMappings.forEach(field => {
      field.mappedTo = null
    })
    
    // Apply auto-mapping
    return this.autoMapFields(headers, fieldMappings)
  }
}

/**
 * Convenience function for auto-mapping
 */
export function autoMapCSVFields(headers: string[], fieldMappings: FieldMapping[]): AutoMappingResult {
  return AutoMappingService.applyAutoMapping(headers, fieldMappings)
}

// Service for handling user feedback and WhatsApp integration

export interface FeedbackContext {
  page: string
  userAgent: string
  timestamp: string
  sessionDuration?: number
  labelsGenerated?: number
}

export interface FeedbackTemplate {
  type: 'general' | 'bug' | 'feature' | 'success' | 'error'
  title: string
  message: string
}

export class FeedbackService {
  private static whatsappNumber = '+233265578245'
  private static sessionStart = Date.now()

  /**
   * Get current page context for feedback
   */
  static getContext(): FeedbackContext {
    const currentPath = window.location.pathname
    const sessionDuration = Math.round((Date.now() - this.sessionStart) / 1000)

    return {
      page: this.getPageName(currentPath),
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      sessionDuration,
      labelsGenerated: this.getLabelsGenerated()
    }
  }

  /**
   * Get user-friendly page name
   */
  private static getPageName(path: string): string {
    const pageMap: Record<string, string> = {
      '/': 'Home Page 🏠',
      '/preview': 'Label Preview 👀',
      '/success': 'Success Page ✅',
    }

    return pageMap[path] || `Page: ${path}`
  }

  /**
   * Get number of labels generated in this session (from localStorage or counter)
   */
  private static getLabelsGenerated(): number {
    try {
      return parseInt(localStorage.getItem('session_labels_generated') || '0')
    } catch {
      return 0
    }
  }

  /**
   * Generate feedback message based on type and context
   */
  static generateMessage(type: FeedbackTemplate['type'] = 'general'): string {
    const context = this.getContext()
    const templates = this.getTemplates()
    const template = templates[type] || templates.general

    return `${template.message}
---
*Sent via Lavvel Feedback Button*`
  }

  /**
   * Get feedback message templates
   */
  private static getTemplates(): Record<FeedbackTemplate['type'], FeedbackTemplate> {
    return {
      general: {
        type: 'general',
        title: 'General Feedback',
        message: `Hi! 👋 I'm using Lavvel and wanted to share some feedback:

🚀 *What I love about Lavvel:*


⚡ *What could be improved:*


💡 *Feature suggestions:*


🐛 *Any issues I encountered:*


Thanks for building this awesome label generation tool! 🎉`
      },

      bug: {
        type: 'bug',
        title: 'Bug Report',
        message: `Hi! 🐛 I found a bug in Lavvel:

❌ *What went wrong:*


🔄 *Steps to reproduce:*
1. 
2. 
3. 

✅ *What I expected to happen:*


📱 *Additional details:*


Please help fix this issue. Thanks! 🙏`
      },

      feature: {
        type: 'feature',
        title: 'Feature Request',
        message: `Hi! 💡 I have a feature idea for Lavvel:

🎯 *Feature Request:*


🤔 *Why this would be helpful:*


📝 *How it could work:*


🔥 *Priority for me:* High / Medium / Low

Thanks for considering this enhancement! 🚀`
      },

      success: {
        type: 'success',
        title: 'Success Story',
        message: `Hi! 🎉 I just had a great experience with Lavvel:

✅ *What worked perfectly:*


😍 *What I loved most:*


📈 *How Lavvel helped me:*


⭐ *Rating:* ⭐⭐⭐⭐⭐

Keep up the amazing work! 🚀`
      },

      error: {
        type: 'error',
        title: 'Error Report',
        message: `Hi! ⚠️ I encountered an error in Lavvel:

🚨 *Error Description:*


📍 *When it happened:*


💥 *Error message (if any):*


🔄 *What I was trying to do:*


Please help resolve this issue. Thanks! 🛠️`
      }
    }
  }

  /**
   * Get simplified browser name
   */
  private static getBrowserName(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome'
    if (userAgent.includes('Firefox')) return 'Firefox'
    if (userAgent.includes('Safari')) return 'Safari'
    if (userAgent.includes('Edge')) return 'Edge'
    return 'Unknown'
  }

  /**
   * Open WhatsApp with feedback message
   */
  static openWhatsApp(type: FeedbackTemplate['type'] = 'general'): void {
    const message = this.generateMessage(type)
    const encodedMessage = encodeURIComponent(message)
    const whatsappUrl = `https://wa.me/${this.whatsappNumber}?text=${encodedMessage}`

    // Open WhatsApp
    window.open(whatsappUrl, '_blank', 'noopener,noreferrer')

    // Track feedback attempt
    this.trackFeedback(type)

    console.log('📱 Opening WhatsApp for feedback:', {
      type,
      page: this.getContext().page,
      messageLength: message.length
    })
  }

  /**
   * Track feedback attempts (optional analytics)
   */
  private static trackFeedback(type: FeedbackTemplate['type']): void {
    try {
      const feedbackCount = parseInt(localStorage.getItem('feedback_count') || '0') + 1
      localStorage.setItem('feedback_count', feedbackCount.toString())
      localStorage.setItem('last_feedback_type', type)
      localStorage.setItem('last_feedback_time', new Date().toISOString())
    } catch (error) {
      console.warn('Could not track feedback:', error)
    }
  }

  /**
   * Get available feedback types for UI
   */
  static getFeedbackTypes(): FeedbackTemplate[] {
    const templates = this.getTemplates()
    return Object.values(templates)
  }

  /**
   * Quick feedback shortcuts
   */
  static quickFeedback = {
    bug: () => this.openWhatsApp('bug'),
    feature: () => this.openWhatsApp('feature'),
    success: () => this.openWhatsApp('success'),
    error: () => this.openWhatsApp('error'),
    general: () => this.openWhatsApp('general')
  }
}

// Export convenience functions
export const sendFeedback = (type?: FeedbackTemplate['type']) => FeedbackService.openWhatsApp(type)
export const reportBug = () => FeedbackService.quickFeedback.bug()
export const requestFeature = () => FeedbackService.quickFeedback.feature()
export const shareSuccess = () => FeedbackService.quickFeedback.success()
export const reportError = () => FeedbackService.quickFeedback.error()

// Make available globally for development
if (import.meta.env.DEV) {
  (window as any).sendFeedback = sendFeedback;
  (window as any).reportBug = reportBug;
  (window as any).requestFeature = requestFeature;
  (window as any).shareSuccess = shareSuccess;
  (window as any).reportError = reportError;

  console.log('💬 Feedback functions available:')
  console.log('  - sendFeedback(type?)')
  console.log('  - reportBug()')
  console.log('  - requestFeature()')
  console.log('  - shareSuccess()')
  console.log('  - reportError()')
}

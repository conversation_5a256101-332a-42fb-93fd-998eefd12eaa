// Service for updating the server counter when PDFs are generated

interface CounterUpdateRequest {
  count: number
  timestamp?: string
  metadata?: {
    labelsGenerated: number
    pdfSize?: number
    userAgent?: string
  }
}

interface CounterUpdateResponse {
  success: boolean
  newTotal: number
  timestamp: string
  message?: string
}

export class CounterUpdateService {
  private endpoint: string
  private retryAttempts: number
  private retryDelay: number

  constructor(endpoint: string, retryAttempts: number = 3, retryDelay: number = 1000) {
    this.endpoint = endpoint
    this.retryAttempts = retryAttempts
    this.retryDelay = retryDelay
  }

  /**
   * Update the server counter with the number of labels generated
   * @param labelsGenerated Number of labels that were successfully generated
   * @param pdfSize Optional PDF file size in bytes
   * @returns Promise with the updated counter response
   */
  async updateCounter(labelsGenerated: number, pdfSize?: number): Promise<CounterUpdateResponse> {
    const requestData: CounterUpdateRequest = {
      count: labelsGenerated,
      timestamp: new Date().toISOString(),
      metadata: {
        labelsGenerated,
        pdfSize,
        userAgent: navigator.userAgent
      }
    }

    console.log(`Updating server counter with ${labelsGenerated} labels generated`)

    let lastError: Error | null = null

    // Retry logic
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const response = await fetch(this.endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData)
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`)
        }

        const data: CounterUpdateResponse = await response.json()

        if (data.success) {
          console.log(`Counter updated successfully. New total: ${data.newTotal}`)
          return data
        } else {
          throw new Error(data.message || 'Server returned success: false')
        }

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')
        console.warn(`Counter update attempt ${attempt}/${this.retryAttempts} failed:`, lastError.message)

        // Wait before retrying (except on last attempt)
        if (attempt < this.retryAttempts) {
          await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt))
        }
      }
    }

    // All attempts failed
    const errorMessage = `Failed to update counter after ${this.retryAttempts} attempts: ${lastError?.message}`
    console.error(errorMessage)
    throw new Error(errorMessage)
  }

  /**
   * Update counter without throwing errors (fire-and-forget)
   * @param labelsGenerated Number of labels generated
   * @param pdfSize Optional PDF size
   */
  async updateCounterSilent(labelsGenerated: number, pdfSize?: number): Promise<void> {
    try {
      await this.updateCounter(labelsGenerated, pdfSize)
    } catch (error) {
      // Log error but don't throw - this is a silent update
      console.error('Silent counter update failed:', error)
    }
  }

  /**
   * Test the connection to the counter endpoint
   */
  async testConnection(): Promise<boolean> {
    try {
      // Try a GET request first to test connectivity
      const response = await fetch(this.endpoint.replace('/update', ''), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      return response.ok
    } catch (error) {
      console.error('Counter endpoint connection test failed:', error)
      return false
    }
  }

  /**
   * Update the endpoint URL
   */
  setEndpoint(newEndpoint: string): void {
    this.endpoint = newEndpoint
  }

  /**
   * Get current endpoint
   */
  getEndpoint(): string {
    return this.endpoint
  }
}

// Create a singleton instance
let counterUpdateService: CounterUpdateService | null = null

/**
 * Get or create the counter update service instance
 */
export function getCounterUpdateService(endpoint?: string): CounterUpdateService {
  if (!counterUpdateService || (endpoint && endpoint !== counterUpdateService.getEndpoint())) {
    const updateEndpoint = endpoint || 'https://api.lavvel.com/counter/update'
    counterUpdateService = new CounterUpdateService(updateEndpoint)
  }
  return counterUpdateService
}

/**
 * Convenience function to update counter
 */
export async function updateServerCounter(labelsGenerated: number, pdfSize?: number, endpoint?: string): Promise<CounterUpdateResponse> {
  const service = getCounterUpdateService(endpoint)
  return service.updateCounter(labelsGenerated, pdfSize)
}

/**
 * Convenience function for silent counter update
 */
export async function updateServerCounterSilent(labelsGenerated: number, pdfSize?: number, endpoint?: string): Promise<void> {
  const service = getCounterUpdateService(endpoint)
  return service.updateCounterSilent(labelsGenerated, pdfSize)
}

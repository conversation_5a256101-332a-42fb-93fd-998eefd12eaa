<script setup lang="ts">
import { onMounted } from 'vue';
import { ImportOrdersButton } from '@/components/orders';
import { Badge } from '@/components/ui/badge';
import { useRealTimeCounter, createCounterConfig } from '@/composables/useRealTimeCounter';
import { setupMockCounterAPI } from '@/utils/mockCounterApi';
import CounterDebugPanel from '@/components/debug/CounterDebugPanel.vue';
import '@/utils/testCounterUpdate'; // Load test utilities in development
import '@/utils/testAutoMapping'; // Load auto-mapping test utilities

const apiURL = "https://lavvel-gen-count.sanflavva.workers.dev"

// Setup mock API for development
// onMounted(() => {
//     if (import.meta.env.DEV) {
//         setupMockCounterAPI(apiURL);
//     }
// });

// Configure the real-time counter
const counterConfig = createCounterConfig({
    endpoint: apiURL, // Your actual endpoint
    pollInterval: 30000, // Poll every 30 seconds
    initialValue: 500000, // Starting value
    enablePolling: true // Set to false to disable polling in development
});

// Use the counter composable
const { formattedCount, isLoading, error, currentCount, lastUpdated, refresh, formatCount, updateCount } = useRealTimeCounter(counterConfig);

// Development mode flag
const isDev = import.meta.env.DEV;
</script>
<template>
    <main class="h-screen overflow-hidden bg-gradient-to-b from-[#2C1F32] to-black flex flex-col">
        <nav class="h-16 text-white flex justify-between items-center p-10">
            <img src="/logo.png" alt="Logo" class="w-36" />
        </nav>
        <div class="h-full flex flex-col justify-between">

            <div class="text-center flex-1 flex flex-col justify-center md:w-1/2 items-center mx-auto gap-5">
                <Badge class="outline mx-auto">Lavvel has a new look!</Badge>
                <h1 class="font-bold text-white text-7xl relative">
                    <span v-if="isLoading && !formattedCount" class="animate-pulse">...</span>
                    <span v-else>{{ formattedCount }} Labels Generated</span>
                    <!-- <span v-if="error"
                        class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-red-400 text-sm">
                        {{ error }}
                    </span> -->
                </h1>
                <p class="text-white px-5">Powerful, self-serve product and label generation to take your business
                    shipping
                    to the
                    next level. Trusted by just one businesses eager to simplify their shipping process.</p>



                <div class=" mx-auto">
                    <ImportOrdersButton></ImportOrdersButton>
                </div>


            </div>

            <div class="flex-1 flex flex-col justify-end">
                <img class="w-full md:w-2/3 mx-auto" src="/hero.png">
            </div>


        </div>

        <!-- Debug Panel (Development Only) -->
        <!-- <CounterDebugPanel v-if="isDev" :currentCount="currentCount" :formattedCount="formattedCount"
            :isLoading="isLoading" :error="error" :lastUpdated="lastUpdated" :refresh="refresh"
            :formatCount="formatCount" :updateCount="updateCount" /> -->
    </main>
</template>
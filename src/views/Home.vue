<script setup lang="ts">
import { ImportOrdersButton } from '@/components/orders';
import { Badge } from '@/components/ui/badge';
</script>
<template>
    <main class="h-screen overflow-hidden bg-gradient-to-b from-[#2C1F32] to-black flex flex-col">
        <nav class="h-16 text-white flex justify-between items-center p-10">
            <img src="/logo.png" alt="Logo" class="w-36" />
        </nav>
        <div class="h-full flex flex-col justify-between">

            <div class="text-center flex-1 flex flex-col justify-center md:w-1/2 items-center mx-auto gap-5">
                <Badge class="outline mx-auto">Lavvel has a new look!</Badge>
                <h1 class="font-bold text-white text-7xl">Lavvel Up!</h1>
                <p class="text-white px-5">Powerful, self-serve product and label generation to take your business
                    shipping
                    to the
                    next level. Trusted by just one business...for now.</p>


                <div class=" mx-auto">
                    <ImportOrdersButton></ImportOrdersButton>
                </div>


            </div>

            <div class="flex-1 flex flex-col justify-end">
                <img class="w-full md:w-2/3 mx-auto" src="/hero.png">
            </div>


        </div>
    </main>



</template>
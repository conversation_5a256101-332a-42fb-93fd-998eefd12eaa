<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useLabelStore } from '@/stores/label'
import { LabelTemplate, DataValidation } from '@/components/label'
import { type Dimension, UnitOfMeasurement } from '@/lib/utils';
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Loader2, ArrowLeft } from 'lucide-vue-next'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useRouter } from 'vue-router';

const labelStore = useLabelStore()

const dimensions = ref<Dimension>({
    height: 6,
    width: 4,
    unit: UnitOfMeasurement.Inches
})

const selectedLabels = ref<boolean[]>([])

const selectedLabelsCount = computed(() => selectedLabels.value.filter(Boolean).length)

const renderedLabels = ref<HTMLElement[]>([])

const labelSizes = [
    { name: '4x6 inches', width: 4, height: 6 },
    { name: '6x6 inches', width: 6, height: 6 },
]

const selectedSize = ref(JSON.stringify(labelSizes[0]))

watch(selectedSize, (newValue) => {
    const size = JSON.parse(newValue)
    updateDimensions(size)
})

const router = useRouter();

watch(() => labelStore.isSuccessfullyExported, (newValue) => {
    if (newValue) router.push('/success');
    labelStore.isSuccessfullyExported = false
});

const updateDimensions = (size: { name: string, width: number, height: number }) => {

    dimensions.value = {
        width: size.width,
        height: size.height,
        unit: UnitOfMeasurement.Inches
    }
}

onMounted(() => {
    if (labelStore.hasImportedLabels) {
        selectedLabels.value = new Array(labelStore.importedLabels.length).fill(true)
    }
    const labelElements = document.querySelectorAll('.label-template')
    renderedLabels.value = Array.from(labelElements) as HTMLElement[]
})

const onValidationConfirm = () => {
    labelStore.confirmValidation()
}

const exportToPDF = async () => {
    try {
        const selectedLabelIndices = selectedLabels.value
            .map((isSelected, index) => isSelected ? index : -1)
            .filter(index => index !== -1)

        const selectedLabelData = selectedLabelIndices.map(index => labelStore.importedLabels[index])
        
        if (selectedLabelData.length === 0) {
            throw new Error('No labels selected')
        }

        await labelStore.generateLabels(dimensions.value, selectedLabelData)
    } catch (error) {
        console.error('Failed to export labels:', error)
        // You might want to show this error to the user through a toast or alert
    }
}

const selectAll = () => {
    selectedLabels.value = selectedLabels.value.map(() => true)
}

const deselectAll = () => {
    selectedLabels.value = selectedLabels.value.map(() => false)
}

const toggleSelectAll = () => {
    if (selectedLabelsCount.value === selectedLabels.value.length) {
        deselectAll()
    } else {
        selectAll()
    }
}



</script>

<template>
    <div class="container mx-auto p-4">
        <div class="flex justify-between md:items-center my-5 flex-col md:flex-row  print:hidden">
            <div>
                <div class="flex items-center mb-4">
                    <Button variant="outline" class="mr-4" @click="$router.back()">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back
                    </Button>
                    <h1 class="text-2xl font-bold">
                        {{ labelStore.isValidating ? 'Data Validation' : 'Labels Preview' }}
                    </h1>
                </div>

            </div>

            <!-- Show these controls only in preview mode -->
            <div v-if="!labelStore.isValidating && labelStore.validationConfirmed" class="flex items-center gap-4 flex-wrap md:flex-nowrap">
                <Select v-model="selectedSize">
                    <SelectTrigger class="w-[180px]">
                        <SelectValue :placeholder="JSON.parse(selectedSize).name" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem v-for="size in labelSizes" :key="size.name" :value="JSON.stringify(size)">
                            {{ size.name }}
                        </SelectItem>
                    </SelectContent>
                </Select>

                <Button v-if="labelStore.importedLabels.length > 0" variant="outline" @click="toggleSelectAll">
                    {{ selectedLabelsCount === selectedLabels.length ? 'Deselect All' : 'Select All' }}
                </Button>

                <Button class="bg-violet-500 hover:bg-violet-600 text-white" @click="exportToPDF"
                    :disabled="labelStore.isExporting || selectedLabelsCount === 0">
                    <Loader2 v-if="labelStore.isExporting" class="animate-spin mr-2" />
                    Export Labels ({{ selectedLabelsCount }})
                </Button>
            </div>
        </div>

        <!-- Data Validation Step -->
        <DataValidation
            v-if="labelStore.isValidating"
            :importedLabels="labelStore.importedLabels"
            @confirm="onValidationConfirm"
            @cancel="$router.back()"
        />

        <!-- Label Preview Step -->
        <div v-else-if="labelStore.hasImportedLabels && labelStore.validationConfirmed">
            <div class="flex flex-wrap justify-evenly gap-8">
                <div v-for="(label, index) in labelStore.importedLabels" :key="index" class="relative"
                    id="label-template">
                    <Checkbox class="absolute -top-2 -left-2 z-10  print:hidden" :id="`label-${index}`"
                        v-model="selectedLabels[index]" :checked="selectedLabels[index]"
                        @update:checked="(checked) => selectedLabels[index] = checked" />

                    <LabelTemplate 
                        :labelDetails="labelStore.formatLabelData(label)"
                        class="label-template"
                        :class="{ 'opacity-50': !selectedLabels[index], 'border-2 border-purple-500 rounded-lg': selectedLabels[index] }"
                        :dimension="dimensions"
                        :showQR="true"
                        :showBarcode="true"
                    />
                </div>
            </div>
        </div>
        <div v-else class="text-center text-gray-500">
            No labels imported yet. Please import labels first.
        </div>
    </div>



</template>

<style scoped>
@media print {
    .print\:hidden {
        display: none !important;
    }

    .print\:block {
        display: block !important;
    }
}
</style>
<template>
    <div class="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
        <!-- Navigation Header -->
        <div class="container mx-auto px-4 pt-6">
            <div class="flex items-center justify-between mb-8">
                <div class="flex items-center space-x-4">
                    <Button variant="outline" @click="$router.push('/preview')" class="hover:bg-gray-50">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back to Preview
                    </Button>
                    <Button variant="outline" @click="$router.push('/')" class="hover:bg-gray-50">
                        <Home class="mr-2 h-4 w-4" />
                        Go Home
                    </Button>
                </div>

                <!-- Quick Actions -->
                <div class="flex items-center space-x-2">
                    <Button variant="outline" size="sm" @click="generateMore"
                        class="text-blue-600 border-blue-200 hover:bg-blue-50">
                        <Plus class="mr-2 h-4 w-4" />
                        Generate More
                    </Button>
                </div>
            </div>
        </div>

        <!-- Main Success Content -->
        <div class="container mx-auto px-4 pb-12">
            <div class="max-w-4xl mx-auto">

                <!-- Success Hero Section -->
                <div class="text-center mb-12">
                    <!-- Animated Success Icon -->
                    <div class="relative mb-8">
                        <div
                            class="flex items-center justify-center w-32 h-32 bg-gradient-to-r from-green-400 to-blue-500 rounded-full mx-auto mb-6 animate-bounce-slow">
                            <CheckCircle class="w-16 h-16 text-white" />
                        </div>
                        <!-- Floating particles effect -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-2 h-2 bg-green-400 rounded-full animate-ping absolute -top-4 -left-4"></div>
                            <div
                                class="w-1 h-1 bg-blue-400 rounded-full animate-ping absolute -bottom-2 -right-6 animation-delay-300">
                            </div>
                            <div
                                class="w-1.5 h-1.5 bg-purple-400 rounded-full animate-ping absolute top-2 -right-8 animation-delay-700">
                            </div>
                        </div>
                    </div>

                    <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                        🎉 Success!
                    </h1>

                    <p class="text-xl text-gray-600 mb-2" v-if="pdfInfo">
                        <span class="font-semibold text-green-600">{{ pdfInfo.labelsCount }}</span>
                        {{ pdfInfo.labelsCount === 1 ? 'label' : 'labels' }} generated successfully
                    </p>

                    <p class="text-gray-500" v-if="pdfInfo">
                        Generated {{ formatTimeAgo(pdfInfo.generatedAt) }}
                    </p>
                </div>

                <!-- PDF Info Card -->
                <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-8" v-if="pdfInfo">
                    <div class="bg-gradient-to-r from-green-500 to-blue-600 px-6 py-4">
                        <h2 class="text-xl font-semibold text-white flex items-center">
                            <FileText class="mr-3 h-6 w-6" />
                            Your PDF is Ready
                        </h2>
                    </div>

                    <div class="p-6">
                        <!-- PDF Stats Grid -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                            <div class="text-center">
                                <div class="bg-blue-50 rounded-lg p-4 mb-2">
                                    <FileText class="w-8 h-8 text-blue-600 mx-auto" />
                                </div>
                                <p class="text-sm text-gray-500">File Size</p>
                                <p class="font-semibold text-gray-900">{{ formatFileSize(pdfInfo.fileSize) }}</p>
                            </div>

                            <div class="text-center">
                                <div class="bg-green-50 rounded-lg p-4 mb-2">
                                    <Package class="w-8 h-8 text-green-600 mx-auto" />
                                </div>
                                <p class="text-sm text-gray-500">Labels</p>
                                <p class="font-semibold text-gray-900">{{ pdfInfo.labelsCount }}</p>
                            </div>

                            <div class="text-center">
                                <div class="bg-purple-50 rounded-lg p-4 mb-2">
                                    <Ruler class="w-8 h-8 text-purple-600 mx-auto" />
                                </div>
                                <p class="text-sm text-gray-500">Dimensions</p>
                                <p class="font-semibold text-gray-900">{{ formatDimensions(pdfInfo.dimensions) }}</p>
                            </div>

                            <div class="text-center">
                                <div class="bg-orange-50 rounded-lg p-4 mb-2">
                                    <Clock class="w-8 h-8 text-orange-600 mx-auto" />
                                </div>
                                <p class="text-sm text-gray-500">Generated</p>
                                <p class="font-semibold text-gray-900">{{ formatTime(pdfInfo.generatedAt) }}</p>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button @click="downloadPDF"
                                class="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                                :disabled="isDownloading">
                                <Download class="mr-3 h-5 w-5" />
                                {{ isDownloading ? 'Downloading...' : 'Download PDF Again' }}
                            </Button>

                            <Button variant="outline" @click="openPrintPreview"
                                class="border-gray-300 hover:bg-gray-50 px-8 py-3 text-lg font-semibold">
                                <Printer class="mr-3 h-5 w-5" />
                                Print Preview
                            </Button>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Grid -->
                <div class="grid md:grid-cols-3 gap-6 mb-8">
                    <!-- Generate More Labels -->
                    <div
                        class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-shadow duration-300">
                        <div class="bg-blue-50 rounded-lg p-3 w-fit mb-4">
                            <Plus class="w-6 h-6 text-blue-600" />
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Generate More Labels</h3>
                        <p class="text-gray-600 text-sm mb-4">Create additional labels with the same or different data
                        </p>
                        <Button variant="outline" @click="generateMore" class="w-full">
                            Start New Generation
                        </Button>
                    </div>

                    <!-- Share Feedback -->
                    <div
                        class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-shadow duration-300">
                        <div class="bg-green-50 rounded-lg p-3 w-fit mb-4">
                            <MessageCircle class="w-6 h-6 text-green-600" />
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Share Your Experience</h3>
                        <p class="text-gray-600 text-sm mb-4">Let us know how Lavvel worked for you</p>
                        <Button variant="outline" @click="shareFeedback" class="w-full">
                            Send Feedback
                        </Button>
                    </div>

                    <!-- View Analytics -->
                    <div
                        class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-shadow duration-300">
                        <div class="bg-purple-50 rounded-lg p-3 w-fit mb-4">
                            <BarChart3 class="w-6 h-6 text-purple-600" />
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Session Summary</h3>
                        <p class="text-gray-600 text-sm mb-4">View your label generation statistics</p>
                        <Button variant="outline" @click="viewStats" class="w-full">
                            View Details
                        </Button>
                    </div>
                </div>

                <!-- Tips Section -->
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <Lightbulb class="mr-2 h-5 w-5 text-yellow-500" />
                        Pro Tips
                    </h3>
                    <div class="grid md:grid-cols-2 gap-4 text-sm text-gray-700">
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>Save your CSV template for faster future label generation</p>
                        </div>
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>Use the same dimensions for consistent label batches</p>
                        </div>
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>Check your printer settings before printing labels</p>
                        </div>
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-orange-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>Keep your PDF files organized with descriptive names</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Button } from '@/components/ui/button'
import {
    ArrowLeft, Download, FileText, Printer, Home, Plus, CheckCircle,
    Package, Ruler, Clock, MessageCircle, BarChart3, Lightbulb
} from 'lucide-vue-next'
import { useLabelStore } from '@/stores/label'
import { useRouter } from 'vue-router'
import { shareSuccess } from '@/services/feedbackService'

const labelStore = useLabelStore()
const router = useRouter()
const isDownloading = ref(false)

// Get PDF info from store
const pdfInfo = computed(() => labelStore.lastGeneratedPDF)

// Redirect if no PDF was generated
onMounted(() => {
    if (!pdfInfo.value) {
        console.warn('No PDF found, redirecting to home')
        router.push('/')
    }
})

/**
 * Download the PDF again
 */
const downloadPDF = async (): Promise<void> => {
    if (!pdfInfo.value) {
        console.error('No PDF available for download')
        return
    }

    isDownloading.value = true
    try {
        labelStore.downloadLastPDF()
        console.log('PDF download initiated')
    } catch (error) {
        console.error('Failed to download PDF:', error)
    } finally {
        // Reset downloading state after a delay
        setTimeout(() => {
            isDownloading.value = false
        }, 2000)
    }
}

/**
 * Open print preview
 */
const openPrintPreview = (): void => {
    if (!pdfInfo.value) {
        console.error('No PDF available for print preview')
        return
    }

    // Create a blob URL and open in new window for printing
    const url = URL.createObjectURL(pdfInfo.value.blob)
    const printWindow = window.open(url, '_blank')

    if (printWindow) {
        printWindow.onload = () => {
            printWindow.print()
            // Clean up the URL after printing
            setTimeout(() => {
                URL.revokeObjectURL(url)
            }, 1000)
        }
    } else {
        console.error('Failed to open print preview window')
    }
}

/**
 * Navigate to generate more labels
 */
const generateMore = (): void => {
    router.push('/')
}

/**
 * Share feedback about success
 */
const shareFeedback = (): void => {
    shareSuccess()
}

/**
 * View session statistics (placeholder)
 */
const viewStats = (): void => {
    // For now, just show an alert with basic stats
    if (pdfInfo.value) {
        alert(`Session Summary:

📊 Labels Generated: ${pdfInfo.value.labelsCount}
📁 File Size: ${formatFileSize(pdfInfo.value.fileSize)}
📐 Dimensions: ${formatDimensions(pdfInfo.value.dimensions)}
⏰ Generated: ${formatTime(pdfInfo.value.generatedAt)}

Thank you for using Lavvel! 🎉`)
    }
}

/**
 * Format file size in human readable format
 */
const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Format dimensions for display
 */
const formatDimensions = (dimensions: any): string => {
    return `${dimensions.width} × ${dimensions.height} ${dimensions.unit}`
}

/**
 * Format time for display
 */
const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

/**
 * Format time ago
 */
const formatTimeAgo = (date: Date): string => {
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
        return 'just now'
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60)
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600)
        return `${hours} hour${hours > 1 ? 's' : ''} ago`
    } else {
        return date.toLocaleDateString()
    }
}
</script>

<style scoped>
/* Custom animations for success page */
@keyframes bounce-slow {

    0%,
    20%,
    53%,
    80%,
    100% {
        transform: translate3d(0, 0, 0);
    }

    40%,
    43% {
        transform: translate3d(0, -15px, 0);
    }

    70% {
        transform: translate3d(0, -7px, 0);
    }

    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.animate-bounce-slow {
    animation: bounce-slow 3s infinite;
}

/* Animation delays for particles */
.animation-delay-300 {
    animation-delay: 300ms;
}

.animation-delay-700 {
    animation-delay: 700ms;
}

/* Hover effects */
.hover\:scale-105:hover {
    transform: scale(1.05);
}

/* Gradient text effect */
.gradient-text {
    background: linear-gradient(135deg, #10b981, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button pulse effect */
@keyframes pulse-success {
    0% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
}

.pulse-success {
    animation: pulse-success 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .animate-bounce-slow {
        animation-duration: 2s;
    }

    .w-32.h-32 {
        width: 6rem;
        height: 6rem;
    }

    .w-16.h-16 {
        width: 3rem;
        height: 3rem;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {

    .animate-bounce-slow,
    .animate-ping,
    .pulse-success {
        animation: none;
    }

    .hover\:scale-105:hover {
        transform: none;
    }

    .card-hover:hover {
        transform: none;
    }
}
</style>
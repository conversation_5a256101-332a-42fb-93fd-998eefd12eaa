<template>
    <div class="container mx-auto p-4">
        <div class="flex flex-col items-center justify-center min-h-screen">
            <div class="flex items-center mb-4">
                <Button variant="outline" class="mr-4" @click="$router.push('/preview')">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    Back to Preview
                </Button>
                <Button variant="outline" class="mr-4" @click="$router.push('/')">
                    <Home class="mr-2 h-4 w-4" />
                    Go Home
                </Button>
            </div>

            <h1 class="text-3xl font-bold mb-6">
                <!-- <bold>{{ labelStore.exportedLabelsCount }}</bold> Labels Generated Successfully! -->
            </h1>

            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
                <p class="font-bold">Generation Complete</p>
                <p>Your labels have been successfully generated and are ready for download.</p>
            </div>

            <div class="flex flex-col items-center space-y-4">
                <div class="flex items-center justify-center w-24 h-24 bg-violet-100 rounded-full mb-4">
                    <FileText class="w-12 h-12 text-violet-500" />
                </div>

                <!-- <p class="text-lg"><strong>File Size:</strong> {{ labelStore.exportedFileSize }}</p> -->

                <div class="flex space-x-4">
                    <Button class="bg-violet-500 hover:bg-violet-600 text-white" @click="downloadPDF">
                        <Download class="mr-2 h-4 w-4" />
                        Download PDF
                    </Button>
                    <Button variant="outline" @click="printPreview">
                        <Printer class="mr-2 h-4 w-4" />
                        Print Preview
                    </Button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { ArrowLeft, Download, FileText, Printer, Home } from 'lucide-vue-next'
import { useLabelStore } from '@/stores/label'
import { useRouter } from 'vue-router'

const labelStore = useLabelStore()
const router = useRouter()

const downloadPDF = () => {
    // labelStore.downloadPDF()
}

const printPreview = () => {
    // labelStore.printPreview()
}
</script>